'use client'

import { useState } from 'react'
import { useAuth } from '@/lib/auth'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { ErrorMessage } from '@/components/ui/ErrorMessage'

type FormType = 'quick_win' | 'suggestion' | 'improvement_full'

export default function TestFormsPage() {
  const [loading, setLoading] = useState(false)
  const [results, setResults] = useState<{[key: string]: any}>({})
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  
  const { user } = useAuth()

  // بيانات اختبار لكويك وين
  const quickWinData = {
    request_number: `QW-${Date.now()}`,
    form_type: 'quick_win',
    form_data: {
      projectTitle: 'مشروع كويك وين - تحسين سرعة النظام',
      section: 'قسم تطوير النظم',
      projectExecutor: {
        name: 'مطور النظام',
        phone: '0501234567',
        email: '<EMAIL>'
      },
      problemDescription: 'بطء في استجابة النظام يؤثر على تجربة المستخدم',
      indicatorName: 'سرعة الاستجابة',
      currentValue: 5,
      targetValue: 2,
      unit: 'ثانية',
      improvementDirection: 'decrease',
      dataSource: 'مراقبة النظام',
      measurementMethod: 'قياس متوسط وقت الاستجابة',
      solution: {
        description: 'تحسين استعلامات قاعدة البيانات وتحسين الكاش',
        tasks: [
          { title: 'تحسين الاستعلامات', assignee: 'مطور قاعدة البيانات' },
          { title: 'تحسين نظام التخزين المؤقت', assignee: 'مطور الواجهة الخلفية' }
        ],
        implementationWeeks: 2,
        estimatedCost: 3000
      },
      teamLeader: {
        name: 'قائد فريق التطوير',
        phone: '0501234568',
        email: '<EMAIL>'
      },
      responsibleDepartment: 'قسم تطوير النظم',
      processDescription: 'عملية معالجة طلبات المستخدمين في النظام',
      problemScope: 'جميع وحدات النظام',
      rootCause: 'استعلامات قاعدة البيانات غير محسنة ونقص في التخزين المؤقت'
    },
    status: 'submitted',
    created_by: user?.id,
    created_at: new Date().toISOString()
  }

  // بيانات اختبار للمقترح
  const suggestionData = {
    request_number: `SUG-${Date.now()}`,
    form_type: 'suggestion',
    form_data: {
      projectName: 'مقترح تحسين نظام إدارة الوثائق',
      problemDescription: 'صعوبة في العثور على الوثائق وتنظيمها',
      indicatorName: 'وقت البحث عن الوثائق',
      currentValue: 10,
      targetValue: 3,
      unit: 'دقيقة',
      improvementDirection: 'decrease',
      dataSource: 'استطلاع المستخدمين',
      measurementMethod: 'قياس متوسط وقت البحث',
      teamLeader: {
        name: 'مدير إدارة الوثائق',
        phone: '0501234569',
        email: '<EMAIL>'
      },
      responsibleDepartment: 'قسم إدارة الوثائق',
      participatingDepartments: ['قسم تطوير النظم', 'قسم الجودة'],
      processDescription: 'عملية تخزين واسترجاع الوثائق في النظام',
      problemScope: 'جميع أنواع الوثائق في النظام',
      rootCause: 'عدم وجود نظام تصنيف واضح وضعف في محرك البحث',
      suggestedSolutions: [
        {
          id: '1',
          title: 'تطوير نظام تصنيف ذكي',
          description: 'إنشاء نظام تصنيف تلقائي للوثائق باستخدام الذكاء الاصطناعي',
          justification: 'يقلل من الوقت المطلوب للبحث ويحسن دقة النتائج',
          expectedBenefits: 'تقليل وقت البحث بنسبة 70%',
          feasibilityScore: 85,
          impactScore: 90,
          priority: 'high'
        },
        {
          id: '2',
          title: 'تحسين محرك البحث',
          description: 'تطوير محرك بحث متقدم مع إمكانيات البحث المتقدم',
          justification: 'يحسن من دقة البحث وسرعة الوصول للوثائق',
          expectedBenefits: 'تحسين دقة البحث بنسبة 60%',
          feasibilityScore: 75,
          impactScore: 80,
          priority: 'medium'
        }
      ]
    },
    status: 'submitted',
    created_by: user?.id,
    created_at: new Date().toISOString()
  }

  // بيانات اختبار للتحسين الشامل
  const enhancedImprovementData = {
    request_number: `EI-${Date.now()}`,
    form_type: 'improvement_full',
    form_data: {
      projectName: 'مشروع تحسين شامل - نظام إدارة الموارد البشرية',
      projectDescription: 'تطوير وتحسين نظام إدارة الموارد البشرية بالكامل',
      startDate: '2024-02-01',
      endDate: '2024-08-31',
      priority: 'high',
      problemDescription: 'نظام الموارد البشرية الحالي قديم وغير فعال',
      indicatorName: 'رضا المستخدمين عن النظام',
      currentValue: 40,
      targetValue: 85,
      unit: '%',
      improvementDirection: 'increase',
      dataSource: 'استطلاع رضا المستخدمين',
      measurementMethod: 'استطلاع شهري للمستخدمين',
      teamLeader: {
        name: 'مدير مشروع الموارد البشرية',
        phone: '0501234570',
        email: '<EMAIL>'
      },
      responsibleDepartment: 'قسم الموارد البشرية',
      participatingDepartments: ['قسم تطوير النظم', 'قسم الجودة', 'قسم الأمن السيبراني'],
      processDescription: 'عمليات إدارة الموظفين والرواتب والإجازات',
      problemScope: 'جميع وحدات نظام الموارد البشرية',
      rootCause: 'تقادم التكنولوجيا المستخدمة وعدم مواكبة المتطلبات الحديثة',
      selectedSolution: {
        description: 'إعادة تطوير النظام بالكامل باستخدام تقنيات حديثة',
        justification: 'الحل الأمثل لتحسين الأداء والوظائف',
        expectedBenefits: 'تحسين الكفاءة بنسبة 200% وتقليل الأخطاء بنسبة 80%',
        estimatedCost: 500000,
        implementationTime: '6 أشهر'
      },
      projectTasks: [
        {
          title: 'تحليل المتطلبات',
          description: 'تحليل مفصل لجميع متطلبات النظام الجديد',
          assignee: 'محلل الأعمال',
          startDate: '2024-02-01',
          endDate: '2024-02-28',
          status: 'pending'
        },
        {
          title: 'تصميم النظام',
          description: 'تصميم هيكل النظام والواجهات',
          assignee: 'مهندس النظم',
          startDate: '2024-03-01',
          endDate: '2024-03-31',
          status: 'pending'
        }
      ],
      requiredResources: [
        {
          type: 'human',
          description: 'فريق تطوير متخصص',
          quantity: 5,
          cost: 200000,
          unit: 'شخص'
        },
        {
          type: 'software',
          description: 'تراخيص البرمجيات',
          quantity: 10,
          cost: 50000,
          unit: 'ترخيص'
        }
      ],
      risks: [
        {
          id: '1',
          description: 'تأخير في التطوير بسبب تعقيد المتطلبات',
          probability: 'medium',
          impact: 'high',
          mitigation: 'تقسيم المشروع إلى مراحل أصغر'
        },
        {
          id: '2',
          description: 'مقاومة التغيير من المستخدمين',
          probability: 'high',
          impact: 'medium',
          mitigation: 'برنامج تدريب مكثف وإشراك المستخدمين في التطوير'
        }
      ]
    },
    status: 'submitted',
    created_by: user?.id,
    created_at: new Date().toISOString()
  }

  const testFormSubmission = async (formType: FormType) => {
    setLoading(true)
    setErrors(prev => ({ ...prev, [formType]: '' }))
    setResults(prev => ({ ...prev, [formType]: null }))

    try {
      let testData
      switch (formType) {
        case 'quick_win':
          testData = quickWinData
          break
        case 'suggestion':
          testData = suggestionData
          break
        case 'improvement_full':
          testData = enhancedImprovementData
          break
        default:
          throw new Error('نوع النموذج غير مدعوم')
      }

      const response = await fetch('/api/requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'حدث خطأ في الاختبار')
      }

      const data = await response.json()
      setResults(prev => ({ ...prev, [formType]: data }))

    } catch (error) {
      console.error(`Test error for ${formType}:`, error)
      setErrors(prev => ({ 
        ...prev, 
        [formType]: error instanceof Error ? error.message : 'حدث خطأ في الاختبار' 
      }))
    } finally {
      setLoading(false)
    }
  }

  const getFormTypeTitle = (type: FormType) => {
    switch (type) {
      case 'quick_win':
        return 'كويك وين'
      case 'suggestion':
        return 'مقترح تحسين'
      case 'improvement_full':
        return 'تحسين شامل'
      default:
        return 'نموذج'
    }
  }

  const getFormTypeDescription = (type: FormType) => {
    switch (type) {
      case 'quick_win':
        return 'مشروع بسيط وسريع التنفيذ'
      case 'suggestion':
        return 'مقترح مع حلول متعددة'
      case 'improvement_full':
        return 'مشروع معقد مع منهجية FOCUS-PDCA'
      default:
        return 'وصف النموذج'
    }
  }

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">اختبار جميع أنواع النماذج</h1>
          <p className="text-gray-600">اختبار شامل لجميع أنواع نماذج طلبات المشاريع</p>
        </div>

        {/* معلومات المستخدم */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">معلومات المستخدم الحالي</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">معرف المستخدم</label>
              <p className="mt-1 text-sm text-gray-900 font-mono">{user?.id || 'غير محدد'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">البريد الإلكتروني</label>
              <p className="mt-1 text-sm text-gray-900">{user?.email || 'غير محدد'}</p>
            </div>
          </div>
        </Card>

        {/* اختبارات النماذج */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {(['quick_win', 'suggestion', 'improvement_full'] as FormType[]).map((formType) => (
            <Card key={formType} className="p-6">
              <div className="text-center mb-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {getFormTypeTitle(formType)}
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {getFormTypeDescription(formType)}
                </p>
                
                <Button
                  onClick={() => testFormSubmission(formType)}
                  disabled={loading || !user?.id}
                  variant="primary"
                  className="w-full"
                >
                  {loading ? 'جاري الاختبار...' : `اختبار ${getFormTypeTitle(formType)}`}
                </Button>
              </div>

              {/* رسائل الخطأ */}
              {errors[formType] && (
                <div className="mb-4">
                  <ErrorMessage 
                    message={errors[formType]} 
                    onClose={() => setErrors(prev => ({ ...prev, [formType]: '' }))}
                  />
                </div>
              )}

              {/* النتائج */}
              {results[formType] && (
                <div className="mb-4">
                  <ErrorMessage 
                    title="تم بنجاح"
                    message={`تم إرسال ${getFormTypeTitle(formType)} بنجاح! رقم الطلب: ${results[formType].request_number || 'غير محدد'}`}
                    type="info"
                    onClose={() => setResults(prev => ({ ...prev, [formType]: null }))}
                  />
                </div>
              )}

              {/* تفاصيل النموذج */}
              <div className="text-xs text-gray-500 space-y-1">
                <p><strong>المراحل:</strong> {formType === 'quick_win' ? '2' : formType === 'suggestion' ? '6' : '9'}</p>
                <p><strong>سير العمل:</strong> {formType === 'quick_win' ? 'بسيط' : formType === 'suggestion' ? 'متوسط' : 'معقد'}</p>
                <p><strong>الموافقات:</strong> {formType === 'quick_win' ? '1 مستوى' : formType === 'suggestion' ? '2 مستوى' : '3 مستوى'}</p>
              </div>
            </Card>
          ))}
        </div>

        {/* تعليمات الاختبار */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">تعليمات الاختبار</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">كويك وين</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• نموذج بسيط مع مرحلتين</li>
                <li>• يحتاج موافقة واحدة</li>
                <li>• للمشاريع السريعة</li>
                <li>• مدة التنفيذ: 1-4 أسابيع</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">مقترح تحسين</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• يحتوي على حلول متعددة</li>
                <li>• يحتاج موافقتين</li>
                <li>• للمقترحات التحسينية</li>
                <li>• مدة التنفيذ: 2-8 أسابيع</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium text-gray-900 mb-2">تحسين شامل</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 9 مراحل متقدمة</li>
                <li>• يحتاج 3 موافقات</li>
                <li>• للمشاريع المعقدة</li>
                <li>• مدة التنفيذ: 2-6 أشهر</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </ProtectedLayout>
  )
} 