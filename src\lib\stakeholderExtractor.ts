// استخراج المشاركين والأقسام المشاركة من بيانات النموذج
import { 
  ParticipantInfo, 
  StakeholderInfo, 
  ReviewerType, 
  ParticipatingDepartment 
} from '@/types/feedback.types';

export class StakeholderExtractor {
  /**
   * استخراج المشاركين في المراجعة (بدون أعضاء الفريق)
   */
  static extractParticipants(formData: any): ParticipantInfo[] {
    const participants: ParticipantInfo[] = [];
    
    try {
      // استخراج قائد الفريق
      if (formData.teamLeader?.name) {
        participants.push({
          id: `team_leader_${formData.teamLeader.name.replace(/\s+/g, '_')}`,
          name: formData.teamLeader.name,
          phone: formData.teamLeader.phone || '',
          email: formData.teamLeader.email,
          role: 'team_leader' as ReviewerType,
          department: formData.teamLeader.department || formData.responsibleDepartment?.name || 'غير محدد',
          position: 'قائد الفريق'
        });
      }

      // استخراج مدير القسم المسؤول
      if (formData.responsibleDepartment?.manager) {
        participants.push({
          id: `dept_manager_${formData.responsibleDepartment.manager.replace(/\s+/g, '_')}`,
          name: formData.responsibleDepartment.manager,
          phone: formData.responsibleDepartment.managerPhone || '',
          email: formData.responsibleDepartment.managerEmail,
          role: 'department_manager' as ReviewerType,
          department: formData.responsibleDepartment.name,
          position: 'مدير القسم المسؤول'
        });
      }

      // استخراج مدراء الأقسام المشاركة
      if (Array.isArray(formData.participatingDepartments)) {
        formData.participatingDepartments.forEach((dept: any, index: number) => {
          if (dept.manager?.name) {
            participants.push({
              id: `participating_manager_${index}_${dept.manager.name.replace(/\s+/g, '_')}`,
              name: dept.manager.name,
              phone: dept.manager.phone || '',
              email: dept.manager.email,
              role: 'participating_department_manager' as ReviewerType,
              department: dept.name,
              position: dept.manager.position || 'مدير القسم المشارك'
            });
          }
        });
      }

      return participants;
    } catch (error) {
      console.error('خطأ في استخراج المشاركين:', error);
      return [];
    }
  }

  /**
   * استخراج الأقسام المشاركة
   */
  static extractParticipatingDepartments(formData: any): ParticipatingDepartment[] {
    const departments: ParticipatingDepartment[] = [];
    
    try {
      // إضافة القسم المسؤول
      if (formData.responsibleDepartment?.name) {
        departments.push({
          id: `responsible_dept_${formData.responsibleDepartment.name.replace(/\s+/g, '_')}`,
          name: formData.responsibleDepartment.name,
          manager: {
            name: formData.responsibleDepartment.manager || 'غير محدد',
            phone: formData.responsibleDepartment.managerPhone || '',
            email: formData.responsibleDepartment.managerEmail,
            position: 'مدير القسم المسؤول'
          },
          involvement: 'primary',
          responsibilities: ['الإشراف الرئيسي على المشروع', 'تنفيذ الحلول المقترحة']
        });
      }

      // إضافة الأقسام المشاركة
      if (Array.isArray(formData.participatingDepartments)) {
        formData.participatingDepartments.forEach((dept: any, index: number) => {
          if (dept.name) {
            departments.push({
              id: `participating_dept_${index}_${dept.name.replace(/\s+/g, '_')}`,
              name: dept.name,
              manager: {
                name: dept.manager?.name || 'غير محدد',
                phone: dept.manager?.phone || '',
                email: dept.manager?.email,
                position: dept.manager?.position || 'مدير القسم'
              },
              involvement: dept.involvement || 'secondary',
              responsibilities: dept.responsibilities || ['دعم تنفيذ المشروع']
            });
          }
        });
      }

      return departments;
    } catch (error) {
      console.error('خطأ في استخراج الأقسام المشاركة:', error);
      return [];
    }
  }

  /**
   * استخراج جميع أصحاب المصلحة من form_data (للتوافق مع النظام القديم)
   */
  static extractStakeholders(formData: any): StakeholderInfo[] {
    // استخدام الدالة الجديدة للمشاركين فقط
    return this.extractParticipants(formData);
  }

  /**
   * استخراج قائد الفريق فقط
   */
  static extractTeamLeader(formData: any): ParticipantInfo | null {
    try {
      if (formData.teamLeader?.name) {
        return {
          id: `team_leader_${formData.teamLeader.name.replace(/\s+/g, '_')}`,
          name: formData.teamLeader.name,
          phone: formData.teamLeader.phone || '',
          email: formData.teamLeader.email,
          role: 'team_leader' as ReviewerType,
          department: formData.teamLeader.department || formData.responsibleDepartment?.name || 'غير محدد',
          position: 'قائد الفريق'
        };
      }
      return null;
    } catch (error) {
      console.error('خطأ في استخراج قائد الفريق:', error);
      return null;
    }
  }

  /**
   * استخراج مدير القسم فقط
   */
  static extractDepartmentManager(formData: any): ParticipantInfo | null {
    try {
      if (formData.responsibleDepartment?.manager) {
        return {
          id: `dept_manager_${formData.responsibleDepartment.manager.replace(/\s+/g, '_')}`,
          name: formData.responsibleDepartment.manager,
          phone: formData.responsibleDepartment.managerPhone || '',
          email: formData.responsibleDepartment.managerEmail,
          role: 'department_manager' as ReviewerType,
          department: formData.responsibleDepartment.name,
          position: 'مدير القسم المسؤول'
        };
      }
      return null;
    } catch (error) {
      console.error('خطأ في استخراج مدير القسم:', error);
      return null;
    }
  }

  /**
   * استخراج مدراء الأقسام المشاركة
   */
  static extractParticipatingManagers(formData: any): ParticipantInfo[] {
    const managers: ParticipantInfo[] = [];
    
    try {
      if (Array.isArray(formData.participatingDepartments)) {
        formData.participatingDepartments.forEach((dept: any, index: number) => {
          if (dept.manager?.name) {
            managers.push({
              id: `participating_manager_${index}_${dept.manager.name.replace(/\s+/g, '_')}`,
              name: dept.manager.name,
              phone: dept.manager.phone || '',
              email: dept.manager.email,
              role: 'participating_department_manager' as ReviewerType,
              department: dept.name,
              position: dept.manager.position || 'مدير القسم المشارك'
            });
          }
        });
      }
      return managers;
    } catch (error) {
      console.error('خطأ في استخراج مدراء الأقسام المشاركة:', error);
      return [];
    }
  }

  /**
   * التحقق من صحة بيانات المشاركين
   */
  static validateParticipantData(formData: any): {
    isValid: boolean;
    missingFields: string[];
    warnings: string[];
  } {
    const missingFields: string[] = [];
    const warnings: string[] = [];

    // التحقق من قائد الفريق
    if (!formData.teamLeader?.name) {
      missingFields.push('اسم قائد الفريق');
    }
    if (!formData.teamLeader?.phone) {
      warnings.push('رقم هاتف قائد الفريق غير محدد');
    }

    // التحقق من مدير القسم
    if (!formData.responsibleDepartment?.manager) {
      missingFields.push('اسم مدير القسم المسؤول');
    }
    if (!formData.responsibleDepartment?.managerPhone) {
      warnings.push('رقم هاتف مدير القسم غير محدد');
    }

    // التحقق من الأقسام المشاركة
    if (Array.isArray(formData.participatingDepartments)) {
      formData.participatingDepartments.forEach((dept: any, index: number) => {
        if (!dept.name) {
          warnings.push(`اسم القسم المشارك ${index + 1} غير محدد`);
        }
        if (!dept.manager?.name) {
          warnings.push(`اسم مدير القسم المشارك ${index + 1} غير محدد`);
        }
      });
    } else {
      warnings.push('لا توجد أقسام مشاركة محددة');
    }

    return {
      isValid: missingFields.length === 0,
      missingFields,
      warnings
    };
  }

  /**
   * التحقق من صحة بيانات أصحاب المصلحة (للتوافق مع النظام القديم)
   */
  static validateStakeholderData(formData: any): {
    isValid: boolean;
    missingFields: string[];
    warnings: string[];
  } {
    return this.validateParticipantData(formData);
  }

  /**
   * تنسيق المشاركين للعرض
   */
  static formatParticipantsForDisplay(participants: ParticipantInfo[]): Array<{
    role: string;
    name: string;
    contact: string;
    department: string;
  }> {
    return participants.map(participant => ({
      role: this.translateRole(participant.role),
      name: participant.name,
      contact: participant.phone + (participant.email ? ` | ${participant.email}` : ''),
      department: participant.department
    }));
  }

  /**
   * تنسيق أصحاب المصلحة للعرض (للتوافق مع النظام القديم)
   */
  static formatStakeholdersForDisplay(stakeholders: StakeholderInfo[]): Array<{
    role: string;
    name: string;
    contact: string;
    department: string;
  }> {
    return this.formatParticipantsForDisplay(stakeholders);
  }

  /**
   * ترجمة أدوار المشاركين
   */
  private static translateRole(role: ReviewerType): string {
    const translations = {
      'team_leader': 'قائد الفريق',
      'department_manager': 'مدير القسم المسؤول',
      'participating_department_manager': 'مدير القسم المشارك',
      'pmo_stakeholder': 'مكتب المشاريع'
    };
    return translations[role] || role;
  }

  /**
   * إنشاء معرف فريد للمشارك
   */
  static generateParticipantId(name: string, role: ReviewerType): string {
    const cleanName = name.replace(/\s+/g, '_').replace(/[^\w\u0600-\u06FF]/g, '');
    const timestamp = Date.now().toString().slice(-6);
    return `${role}_${cleanName}_${timestamp}`;
  }

  /**
   * إنشاء معرف فريد لصاحب المصلحة (للتوافق مع النظام القديم)
   */
  static generateStakeholderId(name: string, role: ReviewerType): string {
    return this.generateParticipantId(name, role);
  }

  /**
   * فلترة المشاركين حسب الدور
   */
  static filterParticipantsByRole(participants: ParticipantInfo[], role: ReviewerType): ParticipantInfo[] {
    return participants.filter(participant => participant.role === role);
  }

  /**
   * فلترة أصحاب المصلحة حسب الدور (للتوافق مع النظام القديم)
   */
  static filterByRole(stakeholders: StakeholderInfo[], role: ReviewerType): StakeholderInfo[] {
    return this.filterParticipantsByRole(stakeholders, role);
  }

  /**
   * البحث عن مشارك بالاسم
   */
  static findParticipantByName(participants: ParticipantInfo[], name: string): ParticipantInfo | null {
    return participants.find(participant => 
      participant.name.toLowerCase().includes(name.toLowerCase())
    ) || null;
  }

  /**
   * البحث عن صاحب مصلحة بالاسم (للتوافق مع النظام القديم)
   */
  static findByName(stakeholders: StakeholderInfo[], name: string): StakeholderInfo | null {
    return this.findParticipantByName(stakeholders, name);
  }

  /**
   * إحصائيات المشاركين
   */
  static getParticipantStatistics(participants: ParticipantInfo[]): {
    total: number;
    byRole: Record<ReviewerType, number>;
    byDepartment: Record<string, number>;
  } {
    const stats = {
      total: participants.length,
      byRole: {} as Record<ReviewerType, number>,
      byDepartment: {} as Record<string, number>
    };

    participants.forEach(participant => {
      // إحصائيات حسب الدور
      stats.byRole[participant.role] = (stats.byRole[participant.role] || 0) + 1;
      
      // إحصائيات حسب القسم
      stats.byDepartment[participant.department] = (stats.byDepartment[participant.department] || 0) + 1;
    });

    return stats;
  }
} 