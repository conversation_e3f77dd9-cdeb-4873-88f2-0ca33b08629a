'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth, usePermissions } from '@/lib/auth'
import { Button } from '@/components/ui/Button'
import { 
  Menu, 
  X, 
  LogOut, 
  User, 
  Bell, 
  Settings,
  Home,
  FolderPlus,
  CheckSquare,
  BarChart3,
  Users,
  Building
} from 'lucide-react'
import { NotificationDropdown } from '@/components/ui/NotificationDropdown'

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  
  const { user, userRole, signOut } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push('/auth/login')
  }

  const navigationItems = [
    {
      name: 'الرئيسية',
      href: '/dashboard',
      icon: Home,
      show: true
    },
    {
      name: 'طلبات المشاريع',
      href: '/requests',
      icon: FolderPlus,
      show: permissions.canViewRequests()
    },
    {
      name: 'الموافقات',
      href: '/approvals',
      icon: CheckSquare,
      show: permissions.canApproveRequests()
    },
    {
      name: 'المشاريع',
      href: '/projects',
      icon: BarChart3,
      show: permissions.canViewProjects()
    },
    {
      name: 'المستخدمين',
      href: '/users',
      icon: Users,
      show: permissions.isAdmin() || permissions.isPMOManager()
    },
    {
      name: 'الأقسام',
      href: '/departments',
      icon: Building,
      show: permissions.isAdmin() || permissions.isPMOManager()
    }
  ]

  const visibleItems = navigationItems.filter(item => item.show)

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
            </div>
            <div className="mr-4">
              <h1 className="heading-main text-gray-900">
                نظام إدارة المشاريع
              </h1>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8 space-x-reverse">
            {visibleItems.map((item) => {
              const Icon = item.icon
              return (
                <button
                  key={item.name}
                  onClick={() => router.push(item.href)}
                  className="flex items-center gap-2 text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-caption font-medium transition-colors"
                >
                  <Icon className="w-4 h-4" />
                  {item.name}
                </button>
              )
            })}
          </nav>

          {/* Right side - Notifications and Profile */}
          <div className="flex items-center gap-4">
            {/* Notifications */}
            <NotificationDropdown />

            {/* Profile Dropdown */}
            <div className="relative">
              <button
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-blue-600" />
                </div>
                <div className="hidden sm:block text-right">
                  <div className="text-caption font-medium text-gray-900">
                    {user?.user_metadata?.name || user?.email}
                  </div>
                  <div className="text-label text-gray-500">
                    {userRole?.display_name}
                  </div>
                </div>
              </button>

              {/* Profile Dropdown Menu */}
              {isProfileOpen && (
                <div className="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50">
                  <button
                    onClick={() => {
                      router.push('/profile')
                      setIsProfileOpen(false)
                    }}
                    className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <User className="w-4 h-4" />
                    الملف الشخصي
                  </button>
                  
                  <button
                    onClick={() => {
                      router.push('/settings')
                      setIsProfileOpen(false)
                    }}
                    className="flex items-center gap-2 w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    <Settings className="w-4 h-4" />
                    الإعدادات
                  </button>
                  
                  <hr className="my-1" />
                  
                  <button
                    onClick={handleSignOut}
                    className="flex items-center gap-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <LogOut className="w-4 h-4" />
                    تسجيل الخروج
                  </button>
                </div>
              )}
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            >
              {isMenuOpen ? (
                <X className="w-5 h-5" />
              ) : (
                <Menu className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <nav className="space-y-2">
              {visibleItems.map((item) => {
                const Icon = item.icon
                return (
                  <button
                    key={item.name}
                    onClick={() => {
                      router.push(item.href)
                      setIsMenuOpen(false)
                    }}
                    className="flex items-center gap-3 w-full px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
                  >
                    <Icon className="w-4 h-4" />
                    {item.name}
                  </button>
                )
              })}
            </nav>
          </div>
        )}
      </div>

      {/* Click outside to close dropdowns */}
      {(isProfileOpen || isMenuOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setIsProfileOpen(false)
            setIsMenuOpen(false)
          }}
        />
      )}
    </header>
  )
} 