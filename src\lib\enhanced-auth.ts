import { supabase } from './supabase'
import { User, Session } from './auth'
import { Database } from '../types/database.types'

// أنواع البيانات للنظام المحسن
export interface LoginAttempt {
  id: string
  email: string
  ip_address?: string
  user_agent?: string
  success: boolean
  failure_reason?: string
  attempted_at: string
}

export interface UserSession {
  id: string
  user_id: string
  session_token: string
  ip_address?: string
  user_agent?: string
  device_info?: any
  login_at: string
  last_activity: string
  logout_at?: string
  is_active: boolean
  expires_at: string
}

export interface AuditLog {
  id: string
  user_id?: string
  action: string
  table_name: string
  record_id?: string
  old_values?: any
  new_values?: any
  ip_address?: string
  user_agent?: string
  session_id?: string
  created_at: string
}

// فئة نظام المصادقة المحسن
export class EnhancedAuthSystem {
  private static instance: EnhancedAuthSystem
  
  private constructor() {}
  
  static getInstance(): EnhancedAuthSystem {
    if (!EnhancedAuthSystem.instance) {
      EnhancedAuthSystem.instance = new EnhancedAuthSystem()
    }
    return EnhancedAuthSystem.instance
  }
  
  /**
   * تسجيل الدخول مع تسجيل العملية
   */
  async signIn(email: string, password: string, metadata?: {
    ip_address?: string
    user_agent?: string
    device_info?: any
  }): Promise<{
    data: { user: User | null; session: Session | null }
    error: any
  }> {
    try {
      // محاولة تسجيل الدخول
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (error) {
        // تسجيل محاولة فاشلة
        await this.logLoginAttempt({
          email,
          ip_address: metadata?.ip_address,
          user_agent: metadata?.user_agent,
          success: false,
          failure_reason: error.message
        })
        
        return { data: { user: null, session: null }, error }
      }
      
      if (data.user && data.session) {
        // تسجيل محاولة ناجحة
        await this.logLoginAttempt({
          email,
          ip_address: metadata?.ip_address,
          user_agent: metadata?.user_agent,
          success: true
        })
        
        // إنشاء جلسة جديدة
        await this.createUserSession({
          user_id: data.user.id,
          session_token: data.session.access_token,
          ip_address: metadata?.ip_address,
          user_agent: metadata?.user_agent,
          device_info: metadata?.device_info
        })
        
        // تسجيل العملية في سجل التدقيق
        await this.logAuditEvent({
          user_id: data.user.id,
          action: 'LOGIN',
          table_name: 'auth.users',
          record_id: data.user.id,
          ip_address: metadata?.ip_address,
          user_agent: metadata?.user_agent,
          session_id: data.session.access_token
        })
        
        // تحويل البيانات إلى النوع المطلوب
        const userResult: User = {
          id: data.user.id,
          email: data.user.email || '',
          name: data.user.user_metadata?.name || '',
          role: data.user.user_metadata?.role || 'user',
          department: data.user.user_metadata?.department || '',
          permissions: data.user.user_metadata?.permissions || {}
        }
        
        const sessionResult: Session = {
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
          expires_at: data.session.expires_at,
          user: userResult
        }
        
        return { data: { user: userResult, session: sessionResult }, error: null }
      }
      
      return { data: { user: null, session: null }, error: null }
    } catch (error) {
      console.error('Enhanced sign in error:', error)
      
      // تسجيل محاولة فاشلة
      await this.logLoginAttempt({
        email,
        ip_address: metadata?.ip_address,
        user_agent: metadata?.user_agent,
        success: false,
        failure_reason: 'System error'
      })
      
      return { 
        data: { user: null, session: null }, 
        error: { message: 'حدث خطأ في النظام' } 
      }
    }
  }
  
  /**
   * تسجيل الخروج مع تسجيل العملية
   */
  async signOut(sessionToken?: string): Promise<{ error: any }> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (user) {
        // تحديث الجلسة كغير نشطة
        if (sessionToken) {
          await this.deactivateUserSession(sessionToken)
        }
        
        // تسجيل العملية في سجل التدقيق
        await this.logAuditEvent({
          user_id: user.id,
          action: 'LOGOUT',
          table_name: 'auth.users',
          record_id: user.id,
          session_id: sessionToken
        })
      }
      
      const { error } = await supabase.auth.signOut()
      return { error }
    } catch (error) {
      console.error('Enhanced sign out error:', error)
      return { error: { message: 'حدث خطأ أثناء تسجيل الخروج' } }
    }
  }
  
  /**
   * تسجيل محاولة تسجيل الدخول
   */
  private async logLoginAttempt(attempt: Omit<LoginAttempt, 'id' | 'attempted_at'>): Promise<void> {
    try {
      const { error } = await supabase.from('login_attempts').insert({
        email: attempt.email,
        ip_address: attempt.ip_address,
        user_agent: attempt.user_agent,
        success: attempt.success,
        failure_reason: attempt.failure_reason
      })
      
      if (error) {
        console.error('Error logging login attempt:', error)
      }
    } catch (error) {
      console.error('Error logging login attempt:', error)
    }
  }
  
  /**
   * إنشاء جلسة مستخدم جديدة
   */
  private async createUserSession(session: Omit<UserSession, 'id' | 'login_at' | 'last_activity' | 'is_active' | 'expires_at' | 'logout_at'>): Promise<void> {
    try {
      const { error } = await supabase.from('user_sessions').insert({
        user_id: session.user_id,
        session_token: session.session_token,
        ip_address: session.ip_address,
        user_agent: session.user_agent,
        device_info: session.device_info
      })
      
      if (error) {
        console.error('Error creating user session:', error)
      }
    } catch (error) {
      console.error('Error creating user session:', error)
    }
  }
  
  /**
   * إلغاء تفعيل جلسة المستخدم
   */
  private async deactivateUserSession(sessionToken: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('user_sessions')
        .update({ 
          is_active: false, 
          logout_at: new Date().toISOString() 
        })
        .eq('session_token', sessionToken)
      
      if (error) {
        console.error('Error deactivating user session:', error)
      }
    } catch (error) {
      console.error('Error deactivating user session:', error)
    }
  }
  
  /**
   * تسجيل حدث في سجل التدقيق
   */
  private async logAuditEvent(event: Omit<AuditLog, 'id' | 'created_at'>): Promise<void> {
    try {
      const { error } = await supabase.from('audit_logs').insert({
        user_id: event.user_id,
        action: event.action,
        table_name: event.table_name,
        record_id: event.record_id,
        old_values: event.old_values,
        new_values: event.new_values,
        ip_address: event.ip_address,
        user_agent: event.user_agent,
        session_id: event.session_id
      })
      
      if (error) {
        console.error('Error logging audit event:', error)
      }
    } catch (error) {
      console.error('Error logging audit event:', error)
    }
  }
  
  /**
   * الحصول على جلسات المستخدم النشطة
   */
  async getUserActiveSessions(userId: string): Promise<UserSession[]> {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('last_activity', { ascending: false })
      
      if (error) throw error
      
      return (data || []).map(session => ({
        id: session.id,
        user_id: session.user_id || '',
        session_token: session.session_token,
        ip_address: session.ip_address as string,
        user_agent: session.user_agent || undefined,
        device_info: session.device_info,
        login_at: session.login_at || '',
        last_activity: session.last_activity || '',
        logout_at: session.logout_at || undefined,
        is_active: session.is_active || false,
        expires_at: session.expires_at || ''
      }))
    } catch (error) {
      console.error('Error getting user sessions:', error)
      return []
    }
  }
  
  /**
   * إنهاء جلسة محددة
   */
  async terminateSession(sessionId: string): Promise<{ error?: any }> {
    try {
      const { error } = await supabase
        .from('user_sessions')
        .update({ 
          is_active: false, 
          logout_at: new Date().toISOString() 
        })
        .eq('id', sessionId)
      
      return { error }
    } catch (error) {
      console.error('Error terminating session:', error)
      return { error: { message: 'فشل في إنهاء الجلسة' } }
    }
  }
  
  /**
   * الحصول على محاولات تسجيل الدخول الأخيرة
   */
  async getRecentLoginAttempts(email?: string, limit: number = 10): Promise<LoginAttempt[]> {
    try {
      let query = supabase
        .from('login_attempts')
        .select('*')
        .order('attempted_at', { ascending: false })
        .limit(limit)
      
      if (email) {
        query = query.eq('email', email)
      }
      
      const { data, error } = await query
      
      if (error) throw error
      
      return (data || []).map(attempt => ({
        id: attempt.id,
        email: attempt.email,
        ip_address: attempt.ip_address as string,
        user_agent: attempt.user_agent || undefined,
        success: attempt.success,
        failure_reason: attempt.failure_reason || undefined,
        attempted_at: attempt.attempted_at || ''
      }))
    } catch (error) {
      console.error('Error getting login attempts:', error)
      return []
    }
  }
  
  /**
   * الحصول على سجل التدقيق
   */
  async getAuditLogs(filters?: {
    user_id?: string
    action?: string
    table_name?: string
    limit?: number
  }): Promise<AuditLog[]> {
    try {
      let query = supabase
        .from('audit_logs')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(filters?.limit || 50)
      
      if (filters?.user_id) {
        query = query.eq('user_id', filters.user_id)
      }
      
      if (filters?.action) {
        query = query.eq('action', filters.action)
      }
      
      if (filters?.table_name) {
        query = query.eq('table_name', filters.table_name)
      }
      
      const { data, error } = await query
      
      if (error) throw error
      
      return (data || []).map(log => ({
        id: log.id,
        user_id: log.user_id || undefined,
        action: log.action,
        table_name: log.table_name,
        record_id: log.record_id || undefined,
        old_values: log.old_values,
        new_values: log.new_values,
        ip_address: log.ip_address as string,
        user_agent: log.user_agent || undefined,
        session_id: log.session_id || undefined,
        created_at: log.created_at || ''
      }))
    } catch (error) {
      console.error('Error getting audit logs:', error)
      return []
    }
  }
  
  /**
   * تنظيف الجلسات المنتهية الصلاحية
   */
  async cleanupExpiredSessions(): Promise<{ cleaned: number; error?: any }> {
    try {
      const { data, error } = await supabase.rpc('cleanup_expired_sessions')
      
      if (error) throw error
      return { cleaned: data || 0 }
    } catch (error) {
      console.error('Error cleaning up sessions:', error)
      return { cleaned: 0, error: { message: 'فشل في تنظيف الجلسات' } }
    }
  }
  
  /**
   * فحص الأمان للمستخدم
   */
  async performSecurityCheck(userId: string): Promise<{
    suspicious_activity: boolean
    failed_attempts: number
    active_sessions: number
    last_login?: string
  }> {
    try {
      // فحص محاولات تسجيل الدخول الفاشلة في آخر ساعة
      const { data: failedAttempts } = await supabase
        .from('login_attempts')
        .select('*')
        .eq('success', false)
        .gte('attempted_at', new Date(Date.now() - 60 * 60 * 1000).toISOString())
      
      // فحص الجلسات النشطة
      const { data: activeSessions } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
      
      // فحص آخر تسجيل دخول ناجح
      const { data: lastLogin } = await supabase
        .from('login_attempts')
        .select('*')
        .eq('success', true)
        .order('attempted_at', { ascending: false })
        .limit(1)
      
      return {
        suspicious_activity: (failedAttempts?.length || 0) > 5,
        failed_attempts: failedAttempts?.length || 0,
        active_sessions: activeSessions?.length || 0,
        last_login: lastLogin?.[0]?.attempted_at
      }
    } catch (error) {
      console.error('Error performing security check:', error)
      return {
        suspicious_activity: false,
        failed_attempts: 0,
        active_sessions: 0
      }
    }
  }
}

// تصدير المثيل الوحيد
export const enhancedAuth = EnhancedAuthSystem.getInstance() 