// واجهات البيانات
export interface LoginData {
  email: string
  password: string
}

export interface User {
  id: string
  email: string
  name: string
  phone?: string
  avatar_url?: string
  department: {
    id: string
    name: string
  }
  role: {
    id: string
    name: string
    display_name: string
    permissions: any
  }
}

export interface AuthResponse {
  success: boolean
  message?: string
  user?: User
  error?: string
}

// دوال API للمصادقة
export class AuthAPI {
  
  // تسجيل الدخول
  static async login(data: LoginData): Promise<AuthResponse> {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في تسجيل الدخول')
      }

      return result
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع'
      }
    }
  }

  // تسجيل الخروج
  static async logout(): Promise<AuthResponse> {
    try {
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في تسجيل الخروج')
      }

      return result
    } catch (error) {
      console.error('Logout error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع'
      }
    }
  }

  // التحقق من الجلسة
  static async verifySession(): Promise<AuthResponse> {
    try {
      const response = await fetch('/api/auth/verify', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'الجلسة غير صالحة')
      }

      return result
    } catch (error) {
      console.error('Session verification error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'حدث خطأ في التحقق من الجلسة'
      }
    }
  }

  // الحصول على المستخدم الحالي
  static async getCurrentUser(): Promise<User | null> {
    try {
      const result = await this.verifySession()
      return result.success ? result.user || null : null
    } catch (error) {
      console.error('Get current user error:', error)
      return null
    }
  }

  // التحقق من الصلاحيات
  static hasPermission(user: User | null, permission: string): boolean {
    if (!user || !user.role) return false
    
    const permissions = user.role.permissions
    
    // مدير النظام له جميع الصلاحيات
    if (permissions.all === true) return true
    
    // التحقق من الصلاحية المحددة
    const [resource, action] = permission.split('.')
    
    if (permissions[resource]) {
      if (Array.isArray(permissions[resource])) {
        return permissions[resource].includes(action)
      }
      return permissions[resource] === true
    }
    
    return false
  }

  // التحقق من الدور
  static hasRole(user: User | null, role: string): boolean {
    if (!user || !user.role) return false
    return user.role.name === role
  }

  // التحقق من أي من الأدوار
  static hasAnyRole(user: User | null, roles: string[]): boolean {
    if (!user || !user.role) return false
    return roles.includes(user.role.name)
  }

  // تحديث بيانات المستخدم
  static async updateProfile(data: Partial<User>): Promise<AuthResponse> {
    try {
      const response = await fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في تحديث الملف الشخصي')
      }

      return result
    } catch (error) {
      console.error('Update profile error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع'
      }
    }
  }

  // تغيير كلمة المرور
  static async changePassword(currentPassword: string, newPassword: string): Promise<AuthResponse> {
    try {
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          current_password: currentPassword,
          new_password: newPassword
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في تغيير كلمة المرور')
      }

      return result
    } catch (error) {
      console.error('Change password error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'حدث خطأ غير متوقع'
      }
    }
  }
}

// دوال مساعدة للمصادقة
export class AuthHelpers {
  
  // تحديد اسم الدور بالعربية
  static getRoleDisplayName(roleName: string): string {
    const roleNames: { [key: string]: string } = {
      admin: 'مدير النظام',
      pmo_manager: 'مدير مكتب المشاريع',
      planning_manager: 'مدير إدارة التخطيط',
      executive_manager: 'المدير التنفيذي',
      project_manager: 'مدير مشروع',
      employee: 'موظف'
    }
    
    return roleNames[roleName] || roleName
  }

  // تحديد لون الدور
  static getRoleColor(roleName: string): string {
    const roleColors: { [key: string]: string } = {
      admin: 'bg-red-100 text-red-800',
      pmo_manager: 'bg-blue-100 text-blue-800',
      planning_manager: 'bg-green-100 text-green-800',
      executive_manager: 'bg-purple-100 text-purple-800',
      project_manager: 'bg-yellow-100 text-yellow-800',
      employee: 'bg-gray-100 text-gray-800'
    }
    
    return roleColors[roleName] || 'bg-gray-100 text-gray-800'
  }

  // تحديد أيقونة الدور
  static getRoleIcon(roleName: string): string {
    const roleIcons: { [key: string]: string } = {
      admin: '👑',
      pmo_manager: '📊',
      planning_manager: '📋',
      executive_manager: '🏢',
      project_manager: '🎯',
      employee: '👤'
    }
    
    return roleIcons[roleName] || '👤'
  }

  // التحقق من صحة البريد الإلكتروني
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // التحقق من قوة كلمة المرور
  static validatePassword(password: string): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []
    
    if (password.length < 8) {
      errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل')
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل')
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل')
    }
    
    if (!/[0-9]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل')
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // تنسيق اسم المستخدم
  static formatUserName(user: User | null): string {
    if (!user) return 'غير محدد'
    return user.name || user.email || 'غير محدد'
  }

  // تنسيق اسم القسم
  static formatDepartmentName(user: User | null): string {
    if (!user || !user.department) return 'غير محدد'
    return user.department.name || 'غير محدد'
  }

  // الحصول على الأحرف الأولى من الاسم
  static getInitials(name: string): string {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }
} 