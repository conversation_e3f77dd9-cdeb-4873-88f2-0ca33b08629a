# 🚀 دليل ربط النظام مع Supabase

## 📋 ملخص التحسينات المطبقة

### ✅ ما تم إنجازه:

#### 1. **تنظيف نظام المصادقة**
- ✅ إزالة النظام المحلي المؤقت
- ✅ الاعتماد على Supabase Auth 100%
- ✅ تبسيط دوال المصادقة
- ✅ إضافة معالجة أخطاء محسنة

#### 2. **تحسين إعدادات البيئة**
- ✅ إنشاء ملف `.env.local` محسن
- ✅ إضافة متغيرات بيئة إضافية للأمان
- ✅ إزالة القيم الافتراضية غير الآمنة

#### 3. **إنشاء أدوات مساعدة**
- ✅ ملف `auth-helpers.ts` لإدارة المستخدمين
- ✅ مكون اختبار الاتصال `SupabaseConnectionTest`
- ✅ صفحة اختبار شاملة

#### 4. **تحسين الأمان**
- ✅ إزالة كلمات المرور من الكود
- ✅ تحسين معالجة الأخطاء
- ✅ إضافة فحص متغيرات البيئة

## 🔧 كيفية التحكم في Supabase

### 1. **من خلال الكود (Supabase Client)**

```typescript
import { supabase } from '@/lib/supabase'

// جلب البيانات
const { data, error } = await supabase
  .from('users')
  .select('*')

// إدراج بيانات
const { data, error } = await supabase
  .from('users')
  .insert({ name: 'اسم المستخدم', email: '<EMAIL>' })

// تحديث بيانات
const { data, error } = await supabase
  .from('users')
  .update({ name: 'اسم جديد' })
  .eq('id', userId)

// حذف بيانات
const { data, error } = await supabase
  .from('users')
  .delete()
  .eq('id', userId)
```

### 2. **من خلال Supabase Management API**

يمكنك استخدام أداة `supabase` المتوفرة في النظام:

```typescript
// مثال: تشغيل استعلام SQL
await supabase({
  summary: "تشغيل استعلام SQL مخصص",
  method: "POST",
  path: "/v1/projects/bxdnhlmrbkazpjhzhjop/database/query",
  data: { query: "SELECT * FROM users LIMIT 10;" }
})

// مثال: تحديث إعدادات المصادقة
await supabase({
  summary: "تحديث إعدادات المصادقة",
  method: "PATCH",
  path: "/v1/projects/bxdnhlmrbkazpjhzhjop/config/auth",
  data: { disable_signup: true }
})
```

### 3. **من خلال Supabase Dashboard**
- انتقل إلى: https://supabase.com/dashboard
- اختر مشروع: `eradah-pmo2025`
- استخدم SQL Editor لتشغيل الاستعلامات

## 🔑 بيانات الدخول الحالية

### المستخدمون الموجودون:
```
<EMAIL> - admin123
<EMAIL> - pmo123  
<EMAIL> - emp123
```

## 🧪 اختبار النظام

### 1. **تشغيل صفحة الاختبار**
```bash
# تشغيل الخادم
npm run dev

# زيارة صفحة الاختبار
http://localhost:3000/supabase-test
```

### 2. **الاختبارات المتاحة**
- ✅ اختبار الاتصال الأساسي
- ✅ اختبار نظام المصادقة
- ✅ فحص جداول قاعدة البيانات
- ✅ اختبار جلب بيانات المستخدمين
- ✅ اختبار تسجيل الدخول

## 📊 إدارة قاعدة البيانات

### الجداول الرئيسية:
- `users` - المستخدمون
- `roles` - الأدوار
- `departments` - الأقسام
- `project_requests` - طلبات المشاريع
- `approvals` - الموافقات
- `projects` - المشاريع
- `tasks` - المهام
- `notifications` - الإشعارات

### أمثلة على الاستعلامات المفيدة:

```sql
-- عرض جميع المستخدمين مع أدوارهم
SELECT u.name, u.email, r.display_name as role, d.name as department
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN departments d ON u.department_id = d.id;

-- عرض طلبات المشاريع
SELECT pr.title, pr.status, u.name as requester
FROM project_requests pr
JOIN users u ON pr.requester_id = u.id
ORDER BY pr.created_at DESC;

-- إحصائيات المستخدمين
SELECT r.display_name, COUNT(*) as user_count
FROM users u
JOIN roles r ON u.role_id = r.id
WHERE u.is_active = true
GROUP BY r.display_name;
```

## 🔒 الأمان والصلاحيات

### Row Level Security (RLS):
- ✅ مفعل على جميع الجداول
- ✅ سياسات أمان مخصصة لكل دور
- ✅ حماية البيانات الحساسة

### أفضل الممارسات:
1. **لا تشارك مفاتيح API** في الكود العام
2. **استخدم متغيرات البيئة** لجميع الإعدادات الحساسة
3. **فعل RLS** على جميع الجداول الجديدة
4. **راجع الصلاحيات** بانتظام

## 🚀 النشر والإنتاج

### متطلبات النشر:
1. **تحديث متغيرات البيئة** في منصة النشر
2. **تغيير مفاتيح التشفير** للإنتاج
3. **تفعيل HTTPS** للأمان
4. **مراجعة سياسات RLS**

### متغيرات البيئة المطلوبة:
```env
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXT_PUBLIC_ENCRYPTION_KEY=your-encryption-key
JWT_SECRET=your-jwt-secret
```

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. خطأ في الاتصال
```
Error: Missing Supabase environment variables
```
**الحل:** تأكد من وجود ملف `.env.local` مع المتغيرات الصحيحة

#### 2. خطأ في المصادقة
```
Error: Invalid credentials
```
**الحل:** تحقق من كلمة المرور أو أنشئ المستخدم في Supabase Auth

#### 3. خطأ في الصلاحيات
```
Error: Row Level Security policy violation
```
**الحل:** راجع سياسات RLS في Supabase Dashboard

## 📞 الدعم والمساعدة

### الموارد المفيدة:
- [Supabase Documentation](https://supabase.com/docs)
- [Next.js with Supabase](https://supabase.com/docs/guides/getting-started/quickstarts/nextjs)
- [Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)

### للحصول على المساعدة:
1. راجع صفحة الاختبار: `/supabase-test`
2. تحقق من وحدة التحكم للأخطاء
3. راجع Supabase Dashboard للسجلات

---

## ✨ الخطوات التالية

### المهام المقترحة:
1. **إضافة المزيد من الاختبارات** للميزات المتقدمة
2. **تحسين معالجة الأخطاء** في جميع أنحاء التطبيق
3. **إضافة نظام إشعارات** في الوقت الفعلي
4. **تحسين الأداء** مع التخزين المؤقت
5. **إضافة نسخ احتياطية** منتظمة

### تحسينات مستقبلية:
- [ ] إضافة Multi-Factor Authentication
- [ ] تحسين نظام الصلاحيات
- [ ] إضافة تدقيق شامل للعمليات
- [ ] تحسين واجهة إدارة المستخدمين
- [ ] إضافة تقارير متقدمة
