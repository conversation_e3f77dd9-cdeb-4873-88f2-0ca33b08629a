import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database.types'

// إعدادات Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://demo-project.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'demo-anon-key-for-development'

// تحديد وضع التطوير
const isDevelopmentMode = process.env.DEVELOPMENT_MODE === 'true'

// إنشاء عميل Supabase
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
  },
})

// نظام مصادقة محلي للتطوير
const localUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    name: 'مدير النظام',
    department: 'إدارة المكتب'
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'pmo123',
    role: 'pmo_manager',
    name: 'مدير مكتب إدارة المشاريع',
    department: 'مكتب إدارة المشاريع'
  },
  {
    id: '3',
    email: '<EMAIL>',
    password: 'emp123',
    role: 'employee',
    name: 'موظف',
    department: 'قسم التطوير'
  }
]

// محاكاة localStorage للجلسة في وضع التطوير
const getStoredSession = () => {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem('demo_session')
    return stored ? JSON.parse(stored) : null
  }
  return null
}

const setStoredSession = (session: any) => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('demo_session', JSON.stringify(session))
  }
}

const clearStoredSession = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('demo_session')
  }
}

// دوال مساعدة للمصادقة
export const auth = {
  /**
   * تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
   */
  signIn: async (email: string, password: string) => {
    // إذا كان في وضع التطوير، استخدم النظام المحلي
    if (isDevelopmentMode) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const user = localUsers.find(u => u.email === email && u.password === password)
      
      if (user) {
        const session = {
          user: {
            id: user.id,
            email: user.email,
            user_metadata: {
              name: user.name,
              role: user.role,
              department: user.department
            }
          },
          access_token: 'demo-token-' + user.id,
          expires_at: Date.now() + (24 * 60 * 60 * 1000)
        }
        
        setStoredSession(session)
        
        return { 
          data: { user: session.user, session }, 
          error: null 
        }
      } else {
        return { 
          data: { user: null, session: null }, 
          error: { message: 'Invalid credentials' } 
        }
      }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    try {
      // أولاً جرب كلمة المرور المؤقتة للمستخدمين الموجودين
      const tempPasswords: { [key: string]: string } = {
        '<EMAIL>': 'admin123',
        '<EMAIL>': 'manager123', 
        '<EMAIL>': 'user123',
        '<EMAIL>': 'newadmin123',
        '<EMAIL>': 'super123',
        '<EMAIL>': 'admin123'
      }
      
      if (tempPasswords[email] && tempPasswords[email] === password) {
        // إنشاء session مؤقت للمستخدم
        const userRole = email.includes('admin') ? 'مدير النظام' : 
                        email.includes('manager') ? 'مدير القسم' : 'موظف'
        
        // تحديد بيانات المستخدم حسب البريد الإلكتروني
        let userId, userName, userDepartment
        
        switch(email) {
          case '<EMAIL>':
            userId = '1'
            userName = 'أحمد محمد'
            userDepartment = 'تقنية المعلومات'
            break
          case '<EMAIL>':
            userId = '2'
            userName = 'فاطمة أحمد'
            userDepartment = 'الموارد البشرية'
            break
          case '<EMAIL>':
            userId = '3'
            userName = 'محمد علي'
            userDepartment = 'المالية'
            break
          case '<EMAIL>':
            userId = '4'
            userName = 'مدير النظام الجديد'
            userDepartment = 'تقنية المعلومات'
            break
          case '<EMAIL>':
            userId = '5'
            userName = 'المدير العام'
            userDepartment = 'الإدارة العليا'
            break
          case '<EMAIL>':
            userId = '430183bf-1deb-4226-9480-64bd049725a5'
            userName = 'مدير النظام'
            userDepartment = 'مكتب إدارة المشاريع'
            break
          default:
            userId = '999'
            userName = 'مستخدم مؤقت'
            userDepartment = 'عام'
        }
        
        const session = {
          user: {
            id: userId,
            email: email,
            user_metadata: {
              name: userName,
              role: userRole,
              department: userDepartment
            }
          },
          access_token: 'temp-token-' + email,
          expires_at: Date.now() + (24 * 60 * 60 * 1000)
        }
        
        setStoredSession(session)
        
        return { 
          data: { user: session.user, session }, 
          error: null 
        }
      }
      
      // إذا لم تنجح كلمة المرور المؤقتة، جرب Supabase Auth
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (error) {
        return { data: { user: null, session: null }, error }
      }
      
      return { data, error: null }
    } catch (error) {
      return { 
        data: { user: null, session: null }, 
        error: { message: 'Authentication failed' } 
      }
    }
  },

  /**
   * تسجيل مستخدم جديد
   */
  signUp: async (email: string, password: string, userData?: any) => {
    if (isDevelopmentMode) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      return { 
        data: { user: null, session: null }, 
        error: { message: 'Registration disabled in demo mode' } 
      }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      })
      
      return { data, error }
    } catch (error) {
      return { 
        data: { user: null, session: null }, 
        error: { message: 'Registration failed' } 
      }
    }
  },

  /**
   * تسجيل الخروج
   */
  signOut: async () => {
    // مسح الجلسة المحفوظة محلياً
    clearStoredSession()
    
    if (isDevelopmentMode) {
      return { error: null }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    try {
      const { error } = await supabase.auth.signOut()
      return { error }
    } catch (error) {
      return { error: { message: 'Sign out failed' } }
    }
  },

  /**
   * الحصول على المستخدم الحالي
   */
  getCurrentUser: async () => {
    if (isDevelopmentMode) {
      const session = getStoredSession()
      
      if (session && session.expires_at > Date.now()) {
        return { user: session.user, error: null }
      } else {
        clearStoredSession()
        return { user: null, error: null }
      }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      return { user, error }
    } catch (error) {
      return { user: null, error: { message: 'Failed to get user' } }
    }
  },

  /**
   * الحصول على الجلسة الحالية
   */
  getCurrentSession: async () => {
    // أولاً تحقق من الجلسة المحفوظة محلياً
    const storedSession = getStoredSession()
    if (storedSession && storedSession.expires_at > Date.now()) {
      return { session: storedSession, error: null }
    }
    
    if (isDevelopmentMode) {
      clearStoredSession()
      return { session: null, error: null }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      return { session, error }
    } catch (error) {
      return { session: null, error: { message: 'Failed to get session' } }
    }
  },

  /**
   * مراقبة تغييرات المصادقة
   */
  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    if (isDevelopmentMode) {
      // في وضع التطوير، لا نحتاج لمراقبة تغييرات المصادقة
      return { data: { subscription: null } }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    return supabase.auth.onAuthStateChange(callback)
  }
}

// دوال مساعدة لقاعدة البيانات
export const database = {
  /**
   * الحصول على جميع المستخدمين
   */
  getUsers: async () => {
    if (isDevelopmentMode) {
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const users = localUsers.map(user => ({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        department: user.department,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))
      
      return { data: users, error: null }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          role:roles(*),
          department:departments!users_department_id_fkey(*)
        `)
      
      return { data, error }
    } catch (error) {
      return { data: null, error: { message: 'Failed to fetch users' } }
    }
  },

  /**
   * الحصول على مستخدم بواسطة المعرف
   */
  getUserById: async (id: string) => {
    if (isDevelopmentMode) {
      await new Promise(resolve => setTimeout(resolve, 300))
      
      const user = localUsers.find(u => u.id === id)
      
      if (user) {
        return { 
          data: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            department: user.department,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }, 
          error: null 
        }
      } else {
        return { data: null, error: { message: 'User not found' } }
      }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    try {
      console.log('getUserById: Fetching user with id:', id)
      // استخدام استعلام بسيط لتجنب مشاكل العلاقات
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single()
      
      console.log('getUserById: User query result:', { userData, userError })
      
      if (userError) {
        return { data: null, error: userError }
      }
      
      // جلب الدور والقسم بشكل منفصل
      const { data: roleData } = userData.role_id ? await supabase
        .from('roles')
        .select('*')
        .eq('id', userData.role_id)
        .single() : { data: null }
      
      const { data: departmentData } = userData.department_id ? await supabase
        .from('departments')
        .select('*')
        .eq('id', userData.department_id)
        .single() : { data: null }
      
      return { 
        data: {
          ...userData,
          role: roleData,
          department: departmentData
        }, 
        error: null 
      }
    } catch (error) {
      console.error('getUserById error:', error)
      return { data: null, error: { message: 'Failed to fetch user' } }
    }
  },

  /**
   * إنشاء طلب مشروع جديد
   */
  createProjectRequest: async (requestData: any) => {
    if (isDevelopmentMode) {
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const newRequest = {
        id: Math.random().toString(36).substr(2, 9),
        ...requestData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      
      return { data: newRequest, error: null }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    try {
      const { data, error } = await supabase
        .from('project_requests')
        .insert(requestData)
        .select()
        .single()
      
      return { data, error }
    } catch (error) {
      return { data: null, error: { message: 'Failed to create project request' } }
    }
  },

  /**
   * الحصول على طلبات المشاريع
   */
  getProjectRequests: async (filters?: any) => {
    if (isDevelopmentMode) {
      await new Promise(resolve => setTimeout(resolve, 600))
      
      // محاكاة بيانات طلبات المشاريع
      const mockRequests = [
        {
          id: '1',
          title: 'تحسين نظام إدارة المخزون',
          description: 'مشروع لتحسين كفاءة إدارة المخزون',
          status: 'under_review',
          main_type: 'improvement_project',
          sub_type: 'improvement_full',
          priority: 'high',
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          title: 'تطوير تطبيق الهاتف المحمول',
          description: 'إنشاء تطبيق جديد للهاتف المحمول',
          status: 'approved',
          main_type: 'general_project',
          sub_type: null,
          priority: 'medium',
          created_at: new Date().toISOString()
        }
      ]
      
      return { data: mockRequests, error: null }
    }
    
    // وضع الإنتاج - استخدام Supabase الحقيقي
    try {
      let query = supabase
        .from('project_requests')
        .select(`
          *,
          requester:users!requester_id(*),
          department:departments(*)
        `)
      
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }
      
      if (filters?.main_type) {
        query = query.eq('main_type', filters.main_type)
      }
      
      const { data, error } = await query.order('created_at', { ascending: false })
      
      return { data, error }
    } catch (error) {
      return { data: null, error: { message: 'Failed to fetch project requests' } }
    }
  }
}

// تصدير معلومات حالة النظام
export const systemInfo = {
  isDevelopmentMode,
  supabaseUrl,
  isConnected: !isDevelopmentMode
}

// دالة فحص مصدر البيانات
export const checkDataSource = () => {
  console.log('=== Data Source Check ===')
  console.log('Development Mode:', isDevelopmentMode)
  console.log('Supabase URL:', supabaseUrl || 'Not configured')
  console.log('Environment Variables:', {
    SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    DEVELOPMENT_MODE: process.env.DEVELOPMENT_MODE
  })
  
  if (isDevelopmentMode) {
    console.log('🔧 Using LOCAL MOCK DATA')
    console.log('Local Users:', localUsers.map(u => ({ id: u.id, email: u.email, name: u.name })))
  } else {
    console.log('🚀 Using REAL SUPABASE DATABASE')
    console.log('Database URL:', supabaseUrl)
  }
  
  return {
    isDevelopmentMode,
    supabaseUrl,
    hasEnvVars: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    dataSource: isDevelopmentMode ? 'LOCAL_MOCK' : 'SUPABASE_REAL'
  }
}