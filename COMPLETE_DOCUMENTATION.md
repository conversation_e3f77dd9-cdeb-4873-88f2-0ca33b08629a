# الدليل الشامل لنظام إدارة طلبات المشاريع
## Complete Documentation Guide

---

## 📋 جدول المحتويات

1. [نظرة عامة على النظام](#نظرة-عامة-على-النظام)
2. [دليل التثبيت والإعداد](#دليل-التثبيت-والإعداد)
3. [دليل المستخدم الإداري](#دليل-المستخدم-الإداري)
4. [إعداد قاعدة البيانات](#إعداد-قاعدة-البيانات)
5. [نظام الأمان والحماية](#نظام-الأمان-والحماية)
6. [نظام الخطوط والتصميم](#نظام-الخطوط-والتصميم)
7. [نظام الإشعارات](#نظام-الإشعارات)
8. [قواعد نجاح المشروع](#قواعد-نجاح-المشروع)
9. [خطة العمل](#خطة-العمل)
10. [استكشاف الأخطاء](#استكشاف-الأخطاء)

---

## 🌟 نظرة عامة على النظام

### الهدف الرئيسي
نظام متكامل لإدارة طلبات المشاريع واعتمادها مع التركيز على مشاريع التحسين باستخدام منهجية FOCUS-PDCA ونظام الموافقات المتدرج.

### المميزات الرئيسية
- **أنواع المشاريع**: مقترح تحسين، طلب مشروع شامل، كويك وين
- **نظام موافقات متدرج**: حسب نوع المشروع ومستوى التعقيد
- **منهجية FOCUS-PDCA**: للتحسين المستمر
- **لوحة Kanban**: لإدارة المشاريع بصرياً
- **نظام أمان متقدم**: مع Row Level Security

### التقنيات المستخدمة
- **Frontend**: Next.js 15.3, React 19, TypeScript
- **Styling**: Tailwind CSS
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **State Management**: Zustand
- **Icons**: Lucide React

---

## 🚀 دليل التثبيت والإعداد

### المتطلبات الأساسية
- Node.js 18+
- npm أو yarn
- حساب Supabase

### خطوات التثبيت

#### 1. استنساخ المشروع
```bash
git clone [repository-url]
cd pmo-system
npm install
```

#### 2. إعداد متغيرات البيئة
إنشاء ملف `.env.local`:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Development Settings
DEVELOPMENT_MODE=false
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

#### 3. تشغيل المشروع
```bash
npm run dev
```

### هيكل المشروع
```
pmo-system/
├── src/
│   ├── app/                    # صفحات Next.js
│   ├── components/             # مكونات React
│   ├── lib/                    # مكتبات ومساعدات
│   └── types/                  # تعريفات TypeScript
├── database/                   # ملفات قاعدة البيانات
└── docs/                       # التوثيق
```

---

## 👤 دليل المستخدم الإداري

### المستخدمون الافتراضيون

#### مدير النظام
- **البريد**: <EMAIL>
- **كلمة المرور**: admin123
- **الصلاحيات**: كاملة

#### مدير مكتب المشاريع
- **البريد**: <EMAIL>
- **كلمة المرور**: pmo123
- **الصلاحيات**: إدارة المشاريع والطلبات

#### موظف عادي
- **البريد**: <EMAIL>
- **كلمة المرور**: user123
- **الصلاحيات**: تقديم الطلبات

### إضافة مستخدم جديد

#### الطريقة الأولى: تحديث الكود
1. افتح `src/lib/supabase.ts`
2. أضف البريد وكلمة المرور في `tempPasswords`
3. أضف بيانات المستخدم في `switch(email)`

#### الطريقة الثانية: قاعدة البيانات
1. افتح لوحة تحكم Supabase
2. اذهب إلى Table Editor > users
3. أضف سجل جديد مع البيانات المطلوبة

### الأدوار والصلاحيات
- **مدير النظام**: صلاحيات كاملة
- **مدير مكتب المشاريع**: إدارة الطلبات والمشاريع
- **مدير إدارة التخطيط**: مراجعة واعتماد المقترحات
- **المدير التنفيذي**: الموافقة النهائية
- **مدير المشروع**: إدارة المشاريع المعينة
- **الموظف**: تقديم الطلبات

---

## 🗄️ إعداد قاعدة البيانات

### الجداول الرئيسية
- **users**: معلومات المستخدمين
- **roles**: الأدوار والصلاحيات
- **departments**: الأقسام
- **project_requests**: طلبات المشاريع
- **approvals**: الموافقات
- **projects**: المشاريع
- **tasks**: المهام
- **notifications**: الإشعارات

### تطبيق مخطط قاعدة البيانات

#### استخدام Supabase Dashboard
1. انتقل إلى Supabase Dashboard
2. اذهب إلى SQL Editor
3. انسخ محتوى `database/schema.sql`
4. قم بتشغيل الكود SQL

#### تطبيق الهجرات
قم بتطبيق ملفات الهجرة بالترتيب:
1. `001_update_project_requests_schema.sql`
2. `002_create_kpi_system.sql`
3. `003_insert_kpi_templates.sql`
4. `004_kpi_permissions.sql`
5. `005_suggestion_feedback_system.sql`

### إعداد Row Level Security
```sql
-- تفعيل RLS على جميع الجداول
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE approvals ENABLE ROW LEVEL SECURITY;
-- ... باقي الجداول
```

---

## 🛡️ نظام الأمان والحماية

### التحسينات الأمنية المطبقة

#### 1. Row Level Security (RLS)
- تم تفعيل RLS على جميع الجداول الحساسة
- سياسات أمان مخصصة لكل جدول
- وصول محدود حسب الأدوار والأذونات

#### 2. نظام تسجيل العمليات
- جدول `audit_logs` لتسجيل جميع العمليات
- محفزات تلقائية للتغييرات
- حفظ القيم القديمة والجديدة

#### 3. إدارة الجلسات
- جدول `user_sessions` لتتبع الجلسات
- انتهاء صلاحية تلقائي
- إمكانية إنهاء الجلسات عن بُعد

#### 4. سياسات كلمات المرور
- دالة `validate_password()` للتحقق من القوة
- متطلبات قابلة للتخصيص
- منع إعادة استخدام كلمات المرور القديمة

### لوحة الأمان
- صفحة `/security` لمراقبة النظام
- إحصائيات شاملة عن الأمان
- إدارة الجلسات النشطة
- تنبيهات الأمان

---

## 🎨 نظام الخطوط والتصميم

### نظام أحجام الخطوط الموحد
تم إعداد نظام موحد يعتمد على 5 أحجام:
- **8px** (text-xs): التفاصيل الدقيقة
- **12px** (text-sm): النصوص الثانوية
- **16px** (text-base): النصوص الأساسية
- **20px** (text-lg): العناوين الفرعية
- **24px** (text-xl): العناوين الرئيسية

### خط النظام
- **الخط الأساسي**: Readex Pro
- **الخط الاحتياطي**: Cairo
- **دعم كامل للعربية والإنجليزية**

### فئات CSS المتاحة
```css
/* الأحجام الأساسية */
.text-micro    /* 8px */
.text-small    /* 12px */
.text-normal   /* 16px */
.text-medium   /* 20px */
.text-large    /* 24px */

/* العناوين */
.heading-main     /* 24px */
.heading-sub      /* 20px */
.heading-section  /* 16px */

/* النصوص */
.text-body     /* 16px */
.text-caption  /* 12px */
.text-label    /* 8px */
```

### مكونات Typography
```tsx
import { 
  MainHeading, 
  SubHeading, 
  SectionHeading,
  BodyText,
  CaptionText,
  LabelText
} from '@/components/ui/Typography'
```

---

## 🔔 نظام الإشعارات

### أنواع الإشعارات

#### 1. Toast Notifications
- إشعارات مؤقتة في أعلى الشاشة
- 5 أنواع: success, error, warning, info, loading
- إزالة تلقائية أو يدوية

#### 2. Form Alerts
- تنبيهات ثابتة داخل النماذج
- دعم التحقق من صحة البيانات
- تنبيهات الحالة التفاعلية

#### 3. System Notifications
- نظام إشعارات متقدم مع قائمة منسدلة
- مرشحات متعددة
- إدارة شاملة للإشعارات

#### 4. Popup Notifications
- إشعارات في زوايا الشاشة
- مواضع متعددة
- تأثيرات انتقالية متقدمة

### الاستخدام
```tsx
import { useToastHelpers } from '@/components/ui/Toast'

const { showSuccess, showError } = useToastHelpers()

// إخطار نجاح
showSuccess('تم الحفظ بنجاح', 'تم حفظ جميع البيانات')

// إخطار خطأ
showError('فشل في الحفظ', 'يرجى المحاولة مرة أخرى')
```

---

## 🎯 قواعد نجاح المشروع

### المبادئ الأساسية

#### 1. التركيز على المستخدم أولاً
- المستخدم هو محور كل قرار
- اختبار كل ميزة مع المستخدمين الفعليين
- معدل رضا المستخدمين > 4.5/5

#### 2. الجودة قبل السرعة
- مراجعة الكود إجبارية
- اختبار شامل لكل ميزة
- Code Coverage > 80%

#### 3. التواصل الفعال
- اجتماعات يومية قصيرة
- تحديثات أسبوعية
- استجابة الفريق < 2 ساعة

### معايير الكود

#### قابلية القراءة
```typescript
// ✅ صحيح - أسماء واضحة
const getUserProjectRequests = async (userId: string) => {
  return await supabase
    .from('project_requests')
    .select('*')
    .eq('user_id', userId);
};
```

#### التوثيق الإجباري
```typescript
/**
 * يقوم بإنشاء طلب مشروع جديد
 * @param requestData - بيانات الطلب
 * @param userId - معرف المستخدم
 * @returns Promise<ProjectRequest>
 */
```

#### معالجة الأخطاء
```typescript
try {
  const result = await createProjectRequest(data, userId);
  return { success: true, data: result };
} catch (error) {
  logger.error('Failed to create project request:', error);
  return { success: false, error: error.message };
}
```

---

## 📅 خطة العمل

### المراحل الرئيسية

#### المرحلة الأولى: التخطيط والتحليل (أسبوعين)
- تحليل المتطلبات
- التصميم المعماري
- اختيار التقنيات

#### المرحلة الثانية: الإعداد والتأسيس (أسبوعين)
- إعداد البيئة التطويرية
- تصميم واجهات المستخدم
- إنشاء Design System

#### المرحلة الثالثة: التطوير الأساسي (6 أسابيع)
- نظام المصادقة والصلاحيات
- لوحة التحكم الرئيسية
- نظام طلبات المشاريع
- نظام الاعتماد والمراجعة
- إدارة المشاريع - Kanban
- التقارير والإحصائيات

#### المرحلة الرابعة: التطوير المتقدم (3 أسابيع)
- الميزات التفاعلية
- تحسينات الأداء
- الاختبار الشامل

#### المرحلة الخامسة: النشر والتسليم (3 أسابيع)
- النشر التجريبي
- التدريب والتوثيق
- النشر النهائي

---

## 🔧 استكشاف الأخطاء

### مشاكل الاتصال

#### خطأ في الاتصال بقاعدة البيانات
```
Error: Invalid API key
```
**الحل**: تأكد من صحة `NEXT_PUBLIC_SUPABASE_ANON_KEY`

#### خطأ في الصلاحيات
```
Error: Row Level Security policy violation
```
**الحل**: تأكد من تطبيق سياسات RLS

### مشاكل المصادقة

#### فشل تسجيل الدخول
1. تحقق من الإملاء الصحيح للبريد الإلكتروني
2. تحقق من كلمة المرور
3. تحقق من إعادة تشغيل الخادم
4. راجع وحدة التحكم للأخطاء

#### عدم ظهور الصلاحيات
1. تحقق من دور المستخدم
2. تحقق من `is_active` = true
3. تحقق من معرف المستخدم الصحيح

### أدوات مفيدة

#### Supabase CLI Commands
```bash
# عرض حالة المشروع
supabase status

# عرض الجداول
supabase db list

# إنشاء هجرة جديدة
supabase migration new migration_name
```

#### SQL مفيد
```sql
-- عرض جميع الجداول
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public';

-- فحص الاتصالات النشطة
SELECT * FROM pg_stat_activity;
```

---

## 📞 الدعم والصيانة

### النسخ الاحتياطي
- يتم حفظ البيانات تلقائياً في Supabase
- يُنصح بإجراء نسخ احتياطية دورية

### التحديثات
- تابع التحديثات الأمنية
- اختبر التحديثات في بيئة التطوير أولاً

### مراقبة النظام
- **صفحة قاعدة البيانات**: `/database`
- **صفحة الأمان**: `/security`
- **صفحة الإحصائيات**: `/analytics`

---

## 🎉 النظام جاهز للاستخدام!

تم إعداد النظام بالكامل وهو جاهز للاستخدام الفعلي. جميع المكونات تعمل بشكل صحيح والاختبارات تؤكد جودة النظام.

### للبدء
1. قم بتسجيل الدخول باستخدام أي من المستخدمين المذكورين
2. استكشف النظام من خلال لوحة التحكم
3. اختبر إنشاء طلبات جديدة
4. تابع المشاريع في لوحة Kanban

### روابط مهمة
- **النظام**: http://localhost:3001
- **تسجيل الدخول**: http://localhost:3001/auth/login
- **الاختبار الشامل**: http://localhost:3001/system-test
- **لوحة التحكم**: http://localhost:3001/dashboard

---

*تم إنشاء هذا الدليل الشامل في: ${new Date().toLocaleString('ar-SA')}*
*يحتوي على جميع المعلومات المجمعة من ملفات التوثيق المختلفة* 