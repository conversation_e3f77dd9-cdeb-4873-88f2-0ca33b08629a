// مساعدات المصادقة والمستخدمين
import { supabase } from './supabase'

// كلمات المرور الافتراضية للمستخدمين الموجودين
export const DEFAULT_PASSWORDS = {
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'pmo123',
  '<EMAIL>': 'emp123'
} as const

// أنواع المستخدمين
export interface UserProfile {
  id: string
  email: string
  name: string
  role?: {
    id: string
    name: string
    display_name: string
  }
  department?: {
    id: string
    name: string
  }
  is_active: boolean
}

/**
 * الحصول على ملف المستخدم الكامل
 */
export async function getUserProfile(userId: string): Promise<{ data: UserProfile | null, error: any }> {
  try {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        role:roles(*),
        department:departments(*)
      `)
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error fetching user profile:', error)
      return { data: null, error }
    }

    return { data, error: null }
  } catch (error) {
    console.error('Exception in getUserProfile:', error)
    return { data: null, error }
  }
}

/**
 * تحديث كلمة مرور المستخدم
 */
export async function updateUserPassword(email: string, newPassword: string) {
  try {
    // هذه الدالة تحتاج صلاحيات admin
    // في الإنتاج، يجب استخدام Supabase Admin API
    console.log(`Password update requested for ${email}`)
    
    // للتطوير، نعرض رسالة
    return {
      success: true,
      message: `كلمة المرور محدثة للمستخدم ${email}`
    }
  } catch (error) {
    console.error('Error updating password:', error)
    return {
      success: false,
      message: 'فشل في تحديث كلمة المرور'
    }
  }
}

/**
 * التحقق من صلاحيات المستخدم
 */
export function checkUserPermission(userRole: string, requiredPermission: string): boolean {
  const rolePermissions: Record<string, string[]> = {
    'admin': ['*'], // جميع الصلاحيات
    'pmo_manager': [
      'view_all_requests',
      'approve_requests',
      'manage_projects',
      'view_reports'
    ],
    'planning_manager': [
      'view_requests',
      'approve_planning',
      'view_reports'
    ],
    'executive_manager': [
      'view_requests',
      'final_approval',
      'view_reports'
    ],
    'project_manager': [
      'view_assigned_projects',
      'manage_tasks',
      'update_progress'
    ],
    'employee': [
      'create_requests',
      'view_own_requests'
    ]
  }

  const permissions = rolePermissions[userRole] || []
  return permissions.includes('*') || permissions.includes(requiredPermission)
}

/**
 * الحصول على قائمة المستخدمين النشطين
 */
export async function getActiveUsers() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select(`
        *,
        role:roles(*),
        department:departments(*)
      `)
      .eq('is_active', true)
      .order('name')

    return { data, error }
  } catch (error) {
    console.error('Error fetching active users:', error)
    return { data: null, error }
  }
}

/**
 * إنشاء مستخدم جديد (يتطلب صلاحيات admin)
 */
export async function createUser(userData: {
  email: string
  name: string
  role_id: string
  department_id?: string
  password?: string
}) {
  try {
    // أولاً، إنشاء المستخدم في Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: userData.email,
      password: userData.password || 'temp123456',
      options: {
        data: {
          name: userData.name
        }
      }
    })

    if (authError) {
      return { data: null, error: authError }
    }

    // ثانياً، إضافة بيانات المستخدم في جدول users
    if (authData.user) {
      const { data: userRecord, error: userError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: userData.email,
          name: userData.name,
          role_id: userData.role_id,
          department_id: userData.department_id,
          is_active: true
        })
        .select()
        .single()

      if (userError) {
        // إذا فشل إنشاء السجل، احذف المستخدم من Auth
        await supabase.auth.admin.deleteUser(authData.user.id)
        return { data: null, error: userError }
      }

      return { data: userRecord, error: null }
    }

    return { data: null, error: { message: 'فشل في إنشاء المستخدم' } }
  } catch (error) {
    console.error('Error creating user:', error)
    return { data: null, error }
  }
}

/**
 * تعطيل/تفعيل مستخدم
 */
export async function toggleUserStatus(userId: string, isActive: boolean) {
  try {
    const { data, error } = await supabase
      .from('users')
      .update({ is_active: isActive })
      .eq('id', userId)
      .select()
      .single()

    return { data, error }
  } catch (error) {
    console.error('Error toggling user status:', error)
    return { data: null, error }
  }
}

/**
 * الحصول على إحصائيات المستخدمين
 */
export async function getUserStats() {
  try {
    const { data: totalUsers } = await supabase
      .from('users')
      .select('id', { count: 'exact' })

    const { data: activeUsers } = await supabase
      .from('users')
      .select('id', { count: 'exact' })
      .eq('is_active', true)

    const { data: usersByRole } = await supabase
      .from('users')
      .select(`
        role:roles(name, display_name),
        count:id
      `)
      .eq('is_active', true)

    return {
      total: totalUsers?.length || 0,
      active: activeUsers?.length || 0,
      byRole: usersByRole || []
    }
  } catch (error) {
    console.error('Error fetching user stats:', error)
    return {
      total: 0,
      active: 0,
      byRole: []
    }
  }
}
