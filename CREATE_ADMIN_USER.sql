-- إنشاء مستخدم مدير جديد في قاعدة البيانات

-- 1. التحقق من الأدوار الموجودة
SELECT * FROM roles;

-- 2. التحقق من الأقسام الموجودة  
SELECT * FROM departments;

-- 3. إنشاء مستخدم مدير جديد
INSERT INTO users (
  id,
  email, 
  name, 
  role_id, 
  department_id,
  is_active,
  created_at,
  updated_at
) VALUES (
  'admin-' || extract(epoch from now())::text,
  '<EMAIL>',  -- غير هذا البريد الإلكتروني
  'اسم المدير الجديد',        -- غير هذا الاسم
  (SELECT id FROM roles WHERE name = 'مدير النظام'),
  (SELECT id FROM departments WHERE name = 'تقنية المعلومات'),
  true,
  NOW(),
  NOW()
);

-- 4. التحقق من المستخدم المُنشأ
SELECT 
  u.id,
  u.email,
  u.name,
  r.name as role_name,
  d.name as department_name,
  u.is_active
FROM users u
JOIN roles r ON u.role_id = r.id
JOIN departments d ON u.department_id = d.id
WHERE u.email = '<EMAIL>';

-- 5. إنشاء كلمة مرور مؤقتة (يتم إضافتها في كود التطبيق)
-- ستحتاج لإضافة البريد الإلكتروني وكلمة المرور في ملف supabase.ts 