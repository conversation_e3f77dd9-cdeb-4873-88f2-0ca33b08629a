'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardHeader, CardContent } from '@/components/ui/Card'
import { useToastHelpers } from '@/components/ui/Toast'
import { LogIn, Mail, Lock, Eye, EyeOff } from 'lucide-react'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  
  const { signIn } = useAuth()
  const router = useRouter()
  const { showSuccess, showError, showLoading, updateLoadingToSuccess, updateLoadingToError } = useToastHelpers()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // التحقق من صحة البيانات
    if (!email.trim()) {
      showError('خطأ في البيانات', 'يرجى إدخال البريد الإلكتروني')
      return
    }
    
    if (!password.trim()) {
      showError('خطأ في البيانات', 'يرجى إدخال كلمة المرور')
      return
    }
    
    if (password.length < 6) {
      showError('خطأ في البيانات', 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      return
    }

    setLoading(true)
    const loadingToastId = showLoading('جاري تسجيل الدخول...', 'يرجى الانتظار')

    try {
      const { data, error } = await signIn(email, password)
      
      if (error) {
        updateLoadingToError(
          loadingToastId,
          'فشل تسجيل الدخول',
          error.message || 'البريد الإلكتروني أو كلمة المرور غير صحيحة'
        )
      } else if (data?.user) {
        updateLoadingToSuccess(
          loadingToastId,
          'تم تسجيل الدخول بنجاح',
          'مرحباً بك في نظام إدارة المشاريع'
        )
        
        // تأخير قصير لإظهار رسالة النجاح
        setTimeout(() => {
          console.log('Redirecting to dashboard...')
          router.push('/dashboard')
        }, 1000)
      } else {
        updateLoadingToError(
          loadingToastId,
          'فشل تسجيل الدخول',
          'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى'
        )
      }
    } catch (err) {
      console.error('Login error:', err)
      updateLoadingToError(
        loadingToastId,
        'خطأ في الاتصال',
        'تعذر الاتصال بالخادم، يرجى التحقق من الاتصال بالإنترنت'
      )
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
            <LogIn className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            نظام إدارة المشاريع
          </h1>
          <p className="text-gray-600">
            مرحباً بك في نظام إدارة طلبات المشاريع واعتمادها
          </p>
        </div>

        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold text-center text-gray-900">
              تسجيل الدخول
            </h2>
          </CardHeader>
          
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                label="البريد الإلكتروني"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="أدخل بريدك الإلكتروني"
                icon={<Mail className="w-5 h-5" />}
                required
              />

              <div className="relative">
                <Input
                  label="كلمة المرور"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="أدخل كلمة المرور"
                  icon={<Lock className="w-5 h-5" />}
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute left-3 top-9 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {showPassword ? (
                    <EyeOff className="w-5 h-5" />
                  ) : (
                    <Eye className="w-5 h-5" />
                  )}
                </button>
              </div>

              <Button
                type="submit"
                variant="primary"
                size="lg"
                loading={loading}
                className="w-full"
                icon={<LogIn className="w-5 h-5" />}
              >
                {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
              </Button>
            </form>
          </CardContent>
        </Card>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            نسيت كلمة المرور؟{' '}
            <button 
              className="text-blue-600 hover:text-blue-700 font-medium"
              onClick={() => router.push('/auth/reset-password')}
            >
              إعادة تعيين
            </button>
          </p>
        </div>

        <div className="mt-8 bg-blue-50 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-2">حسابات تجريبية:</h3>
          <div className="text-sm text-blue-700 space-y-1">
            <p><strong>مدير النظام:</strong> <EMAIL> / admin123</p>
            <p><strong>مدير جديد:</strong> <EMAIL> / newadmin123</p>
            <p><strong>المدير العام:</strong> <EMAIL> / super123</p>
            <p><strong>مدير قسم:</strong> <EMAIL> / manager123</p>
            <p><strong>موظف:</strong> <EMAIL> / user123</p>
          </div>
        </div>
      </div>
    </div>
  )
} 