/**
 * نظام أحجام الخطوط الموحد - محسن للداشبورد - مصغر بنسبة 10%
 * التوزيع: 9px - 11px - 13px - 14px - 16px
 */

export const FontSizes = {
  // الأحجام الأساسية - مصغرة بنسبة 10%
  micro: '9px',    // text-xs - للتفاصيل الدقيقة والملاحظات الصغيرة
  small: '11px',   // text-sm - للنصوص الثانوية والتسميات
  normal: '13px',  // text-base - للنصوص الأساسية والمحتوى
  medium: '14px',  // text-lg - للعناوين الفرعية والمهمة
  large: '16px',   // text-xl - للعناوين الرئيسية والمهمة جداً
} as const;

export const FontSizeClasses = {
  // فئات Tailwind المحدثة - مصغرة بنسبة 10%
  micro: 'text-xs',    // 9px
  small: 'text-sm',    // 11px
  normal: 'text-base', // 13px
  medium: 'text-lg',   // 14px
  large: 'text-xl',    // 16px
} as const;

export const FontSizeUsage = {
  // استخدامات الأحجام
  micro: [
    'أيقونات صغيرة',
    'تسميات الحقول الصغيرة',
    'رقم الصفحة',
    'تواريخ صغيرة',
    'badges صغيرة'
  ],
  small: [
    'تسميات الحقول',
    'نصوص مساعدة',
    'رسائل الأخطاء',
    'تواريخ وأوقات',
    'نصوص ثانوية'
  ],
  normal: [
    'النصوص الأساسية',
    'محتوى الصفحات',
    'النماذج',
    'الأزرار',
    'القوائم'
  ],
  medium: [
    'العناوين الفرعية',
    'أسماء الأقسام',
    'عناوين الكروت',
    'النصوص المهمة',
    'عناوين الجداول'
  ],
  large: [
    'العناوين الرئيسية',
    'عناوين الصفحات',
    'رسائل الحالة المهمة',
    'العناوين الكبيرة',
    'نصوص الترحيب'
  ]
} as const;

export const CustomFontClasses = {
  // فئات CSS مخصصة
  micro: 'text-micro',
  small: 'text-small', 
  normal: 'text-normal',
  medium: 'text-medium',
  large: 'text-large',
  
  // فئات العناوين
  headingMain: 'heading-main',     // 18px
  headingSub: 'heading-sub',       // 16px
  headingSection: 'heading-section', // 14px
  
  // فئات النصوص
  body: 'text-body',       // 14px
  caption: 'text-caption', // 12px
  label: 'text-label',     // 10px
} as const;

export type FontSizeType = keyof typeof FontSizes;
export type FontSizeClass = keyof typeof FontSizeClasses;
export type CustomFontClass = keyof typeof CustomFontClasses; 