# 📌 قواعد كتابة الكود داخل مشروع إدارة طلبات المشاريع (PMO System)

## 1. الهيكل العام
- كل وحدة (module) يجب أن تكون صغيرة، واضحة، وقابلة للاختبار
- لا يُسمح بتجاوز 180 سطر لكل ملف
- تقسيم الملفات حسب الوظيفة وليس حسب التقنية (feature-based not layer-based)

## 2. الأسلوب والتسمية
- استخدم أسماء صريحة ومعبرة لكل شيء (functions, variables, components)
- لا اختصارات - لا رموز غامضة - لا أسماء مؤقتة مثل `temp`, `foo`, `xyz`

## 3. التفكير المنطقي
- لا تبدأ بكتابة الحل قبل فهم المشكلة بالكامل
- اسأل نفسك: من المستخدم؟ ما السيناريو؟ أين يُستخدم الكود؟
- لا تفترض: اعتمد فقط على المعطيات من السياق أو التعليقات أو الملفات المرتبطة

## 4. التجزئة المنهجية
- كل وظيفة (Function) لا تتعدى 50 سطر
- لا تُجَمِّع أكثر من حالة استخدام في دالة واحدة
- اجعل الدالة تؤدي غرض واحد فقط (Single Responsibility)

## 5. الأمن والجودة
- لا تُرسل أي بيانات قبل التحقق منها (Validation + Sanitization)
- أي كود يكتب يجب أن يُرافقه اختبار (Unit أو Integration)
- كل عملية مهمة تُسجّل في audit log

## 6. المخرجات التفاعلية
- أي عملية async يجب أن تظهر حالة التحميل للمستخدم
- أي خطأ يجب أن يعرض رسالة واضحة ومترجمة للمستخدم النهائي
- لا تترك حالات غير معالجة (Unhandled States)

## 7. الثبات والتنظيم
- استخدم نفس أنماط التصميم عبر المشروع: استدعاء API، عرض جداول، عرض خطأ، تنقل المستخدم
- كل صفحة لها ملف CSS خاص أو MUI theme ثابت
- جميع المكونات UI يجب أن تُستورد من مجلد مشترك (shared/components)

## 8. التفاعل مع كيرسر
- لا تتردد في إعادة شرح السياق إذا لاحظت خلل في المخرجات
- راجع كل كود يولده كيرسر، خصوصاً ما يخص التحقق، الأمن، والاستعلامات
- إذا أعطاك كود في ملف خاطئ، انقله يدويًا للملف الصحيح ووضح ذلك في التعليق

## 9. التجنب الصارم لما يلي:
- لا تستخدم أي كود "مؤقت" بدون TODO واضح
- لا تكتب منطق معقد في JSX مباشرة
- لا تعتمد على console.log في الإنتاج
- لا تكتب كود بدون تعليق إذا كان فيه منطق غير بديهي

## 10. التوثيق والتعليمات
- كل وحدة يجب أن تحتوي على:
  - Comment أعلى الملف يشرح دوره
  - Description مختصر لكل دالة عامة
  - Type definitions في ملفات مستقلة إن أمكن

---

---
description:
globs:
alwaysApply: false
---
