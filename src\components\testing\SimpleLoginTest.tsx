'use client'

import { useState } from 'react'
import { auth } from '@/lib/supabase'
import { CheckCircle, XCircle, Loader2, LogIn } from 'lucide-react'

export default function SimpleLoginTest() {
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [selectedUser, setSelectedUser] = useState('<EMAIL>')

  const testUsers = [
    { email: '<EMAIL>', password: 'admin123', name: 'مدير النظام' },
    { email: '<EMAIL>', password: 'pmo123', name: 'مدير مكتب إدارة المشاريع' },
    { email: '<EMAIL>', password: 'emp123', name: 'موظف' }
  ]

  const testLogin = async () => {
    setIsLoading(true)
    setResult(null)

    const user = testUsers.find(u => u.email === selectedUser)
    if (!user) return

    try {
      console.log('Attempting login for:', user.email)
      
      const { data, error } = await auth.signIn(user.email, user.password)
      
      if (error) {
        console.error('Login error:', error)
        setResult({
          success: false,
          message: error.message,
          details: error
        })
      } else {
        console.log('Login success:', data)
        setResult({
          success: true,
          message: 'تم تسجيل الدخول بنجاح',
          user: data.user,
          session: data.session
        })
      }
    } catch (error: any) {
      console.error('Login exception:', error)
      setResult({
        success: false,
        message: 'خطأ غير متوقع',
        details: error
      })
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    try {
      await auth.signOut()
      setResult(null)
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center gap-3 mb-6">
          <LogIn className="h-6 w-6 text-blue-600" />
          <h2 className="text-2xl font-bold text-gray-900">اختبار تسجيل الدخول البسيط</h2>
        </div>

        {/* اختيار المستخدم */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            اختر المستخدم للاختبار:
          </label>
          <select
            value={selectedUser}
            onChange={(e) => setSelectedUser(e.target.value)}
            className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            {testUsers.map((user) => (
              <option key={user.email} value={user.email}>
                {user.name} ({user.email})
              </option>
            ))}
          </select>
        </div>

        {/* زر الاختبار */}
        <div className="mb-6">
          <button
            onClick={testLogin}
            disabled={isLoading}
            className="w-full flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <LogIn className="h-4 w-4" />
            )}
            {isLoading ? 'جاري تسجيل الدخول...' : 'اختبار تسجيل الدخول'}
          </button>
        </div>

        {/* النتيجة */}
        {result && (
          <div className={`p-4 rounded-lg border ${
            result.success 
              ? 'bg-green-50 border-green-200' 
              : 'bg-red-50 border-red-200'
          }`}>
            <div className="flex items-center gap-2 mb-3">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
              <h3 className={`font-semibold ${
                result.success ? 'text-green-900' : 'text-red-900'
              }`}>
                {result.success ? 'نجح تسجيل الدخول' : 'فشل تسجيل الدخول'}
              </h3>
            </div>
            
            <p className={`text-sm mb-3 ${
              result.success ? 'text-green-700' : 'text-red-700'
            }`}>
              {result.message}
            </p>

            {result.success && result.user && (
              <div className="space-y-2 text-sm">
                <div><strong>البريد الإلكتروني:</strong> {result.user.email}</div>
                <div><strong>المعرف:</strong> {result.user.id}</div>
                {result.user.user_metadata?.name && (
                  <div><strong>الاسم:</strong> {result.user.user_metadata.name}</div>
                )}
                {result.user.user_metadata?.role && (
                  <div><strong>الدور:</strong> {result.user.user_metadata.role}</div>
                )}
                {result.user.user_metadata?.department && (
                  <div><strong>القسم:</strong> {result.user.user_metadata.department}</div>
                )}
                
                <button
                  onClick={logout}
                  className="mt-3 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                >
                  تسجيل الخروج
                </button>
              </div>
            )}

            {!result.success && result.details && (
              <details className="mt-3">
                <summary className="cursor-pointer text-sm font-medium">
                  تفاصيل الخطأ
                </summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify(result.details, null, 2)}
                </pre>
              </details>
            )}
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-900 mb-2">معلومات الاختبار:</h4>
          <div className="text-sm text-gray-600 space-y-1">
            <div>• تم إعادة إنشاء جميع المستخدمين في قاعدة البيانات</div>
            <div>• كلمات المرور محدثة ومشفرة بشكل صحيح</div>
            <div>• المستخدمون مؤكدون ونشطون</div>
          </div>
        </div>
      </div>
    </div>
  )
}
