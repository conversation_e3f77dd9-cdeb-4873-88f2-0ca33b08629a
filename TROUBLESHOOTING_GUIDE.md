# 🔧 دليل استكشاف الأخطاء وإصلاحها

## 🚨 الأخطاء الشائعة وحلولها

### 1. خطأ في العلاقات (Relationship Error)
```
Could not embed because more than one relationship was found for 'users' and 'departments'
```

**السبب:** مشكلة في تعريف العلاقات في Supabase
**الحل:** تم إصلاح هذا بتبسيط الاستعلامات وجلب البيانات بشكل منفصل

### 2. خطأ في المصادقة (Auth Error)
```
AuthApiError: Database error querying schema
```

**الأسباب المحتملة:**
- كلمة المرور غير صحيحة
- المستخدم غير موجود في Supabase Auth
- مشكلة في إعدادات قاعدة البيانات

**الحلول:**
1. **إعادة تعيين كلمة المرور:**
```sql
UPDATE auth.users 
SET encrypted_password = crypt('admin123', gen_salt('bf')) 
WHERE email = '<EMAIL>';
```

2. **إنشاء المستخدم من جديد:**
- استخدم زر "إعداد المستخدمين" في صفحة الاختبار
- أو أنشئ المستخدم يدوياً في Supabase Dashboard

### 3. خطأ في متغيرات البيئة
```
Error: Missing Supabase environment variables
```

**الحل:**
1. تأكد من وجود ملف `.env.local`
2. تحقق من المتغيرات المطلوبة:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

### 4. خطأ في الاتصال بقاعدة البيانات
```
Error: Failed to fetch
```

**الحلول:**
1. تحقق من اتصال الإنترنت
2. تأكد من صحة رابط Supabase
3. تحقق من حالة خدمة Supabase

## 🛠️ خطوات الإصلاح المتقدمة

### إعادة إنشاء المستخدمين
```sql
-- حذف المستخدمين الموجودين
DELETE FROM auth.users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');
DELETE FROM users WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');

-- إنشاء المستخدمين من جديد (استخدم Supabase Dashboard أو API)
```

### فحص العلاقات
```sql
-- فحص المفاتيح الخارجية
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu 
    ON tc.constraint_name = kcu.constraint_name 
JOIN information_schema.constraint_column_usage AS ccu 
    ON ccu.constraint_name = tc.constraint_name 
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_name = 'users';
```

### فحص البيانات
```sql
-- فحص المستخدمين مع أدوارهم وأقسامهم
SELECT 
    u.id, 
    u.email, 
    u.name, 
    r.name as role_name, 
    d.name as department_name
FROM users u
LEFT JOIN roles r ON u.role_id = r.id
LEFT JOIN departments d ON u.department_id = d.id;
```

## 🔍 أدوات التشخيص

### 1. صفحة الاختبار
- انتقل إلى: `http://localhost:3000/supabase-test`
- شغل جميع الاختبارات
- راجع النتائج والأخطاء

### 2. وحدة التحكم في المتصفح
- افتح Developer Tools (F12)
- راجع تبويب Console للأخطاء
- راجع تبويب Network لطلبات API

### 3. Supabase Dashboard
- انتقل إلى: https://supabase.com/dashboard
- راجع Logs للأخطاء
- استخدم SQL Editor لتشغيل الاستعلامات

## 📋 قائمة فحص سريعة

### قبل البدء:
- [ ] ملف `.env.local` موجود ومكتمل
- [ ] الخادم يعمل (`npm run dev`)
- [ ] Supabase متاح ويعمل

### عند حدوث خطأ:
- [ ] راجع وحدة التحكم للأخطاء
- [ ] تحقق من صفحة الاختبار
- [ ] راجع Supabase Logs
- [ ] تأكد من صحة البيانات في قاعدة البيانات

### للإصلاح:
- [ ] جرب إعادة تشغيل الخادم
- [ ] استخدم زر "إعداد المستخدمين"
- [ ] أعد تعيين كلمات المرور
- [ ] تحقق من العلاقات في قاعدة البيانات

## 🆘 طلب المساعدة

إذا استمرت المشاكل:

1. **اجمع المعلومات:**
   - رسالة الخطأ الكاملة
   - خطوات إعادة إنتاج المشكلة
   - لقطة شاشة من وحدة التحكم

2. **راجع السجلات:**
   - Supabase Dashboard > Logs
   - Browser Console
   - Terminal Output

3. **جرب الحلول البديلة:**
   - استخدم Supabase Dashboard مباشرة
   - جرب إنشاء مستخدم جديد
   - تحقق من إعدادات المشروع

## 📚 موارد إضافية

- [Supabase Documentation](https://supabase.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

---

## 🔄 التحديثات الأخيرة

### ما تم إصلاحه:
- ✅ مشاكل العلاقات في الاستعلامات
- ✅ أخطاء المصادقة
- ✅ تبسيط جلب البيانات
- ✅ إضافة أدوات إعداد المستخدمين

### التحسينات المضافة:
- ✅ دوال منفصلة لجلب العلاقات
- ✅ معالجة أخطاء محسنة
- ✅ أدوات تشخيص شاملة
- ✅ زر إعداد المستخدمين التلقائي
