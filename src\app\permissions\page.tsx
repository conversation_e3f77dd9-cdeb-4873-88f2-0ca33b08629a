'use client'

import React, { useState } from 'react'
import { Shield, Users, Settings, UserPlus } from 'lucide-react'
import { ProtectedLayout } from '@/components/layout/ProtectedLayout'
import { useAuth, usePermissions } from '@/lib/auth'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

// استيراد المكونات
import RolesManagement from '@/components/permissions/RolesManagement'
import PermissionsManagement from '@/components/permissions/PermissionsManagement'
import UsersRoleAssignment from '@/components/permissions/UsersRoleAssignment'
import PermissionsOverview from '@/components/permissions/PermissionsOverview'

type TabType = 'overview' | 'roles' | 'permissions' | 'users'

export default function PermissionsPage() {
  const [activeTab, setActiveTab] = useState<TabType>('overview')
  const { user, loading } = useAuth()
  const permissions = usePermissions()
  const router = useRouter()

  // التحقق من الصلاحيات
  useEffect(() => {
    if (!loading && user) {
      if (!permissions.isAdmin() && !permissions.isPMOManager()) {
        router.push('/dashboard')
      }
    }
  }, [user, loading, permissions, router])

  const tabs = [
    {
      id: 'overview' as TabType,
      label: 'نظرة عامة',
      icon: Shield,
      description: 'عرض شامل للصلاحيات الحالية'
    },
    {
      id: 'roles' as TabType,
      label: 'إدارة الأدوار',
      icon: Users,
      description: 'إضافة وتعديل الأدوار'
    },
    {
      id: 'permissions' as TabType,
      label: 'إدارة الصلاحيات',
      icon: Settings,
      description: 'تحديد صلاحيات كل دور'
    },
    {
      id: 'users' as TabType,
      label: 'تعيين الأدوار',
      icon: UserPlus,
      description: 'ربط المستخدمين بالأدوار'
    }
  ]

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <PermissionsOverview />
      case 'roles':
        return <RolesManagement />
      case 'permissions':
        return <PermissionsManagement />
      case 'users':
        return <UsersRoleAssignment />
      default:
        return <PermissionsOverview />
    }
  }

  if (loading) {
    return (
      <ProtectedLayout>
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </ProtectedLayout>
    )
  }

  // التحقق من الصلاحيات
  if (!permissions.isAdmin() && !permissions.isPMOManager()) {
    return (
      <ProtectedLayout>
        <div className="text-center py-12">
          <Shield className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            غير مسموح بالوصول
          </h3>
          <p className="text-gray-600">
            ليس لديك صلاحية للوصول إلى هذه الصفحة
          </p>
        </div>
      </ProtectedLayout>
    )
  }

  return (
    <ProtectedLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Shield className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">
              إدارة الصلاحيات والأدوار
            </h1>
          </div>
          <p className="text-gray-600 text-lg">
            تحكم في صلاحيات المستخدمين وأدوارهم في النظام
          </p>
        </div>

        {/* Tabs Navigation */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 rtl:space-x-reverse">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    {tab.label}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Tab Description */}
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-blue-800 text-sm">
              {tabs.find(tab => tab.id === activeTab)?.description}
            </p>
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white">
          {renderTabContent()}
        </div>
      </div>
    </ProtectedLayout>
  )
} 