import { createClient } from '@supabase/supabase-js'
import { Database } from '@/types/database.types'

// التحقق من وجود متغيرات البيئة المطلوبة
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env.local file.')
}

// إنشاء عميل Supabase مع إعدادات محسنة
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    flowType: 'pkce',
  },
  db: {
    schema: 'public',
  },
  global: {
    headers: {
      'x-application-name': 'PMO-System',
    },
  },
})

// دوال مساعدة للمصادقة
export const auth = {
  /**
   * تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
   */
  signIn: async (email: string, password: string) => {
    try {
      // استخدام Supabase Auth
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        console.error('Supabase Auth Error:', error)
        return {
          data: { user: null, session: null },
          error: { message: 'فشل في تسجيل الدخول. تحقق من بيانات الدخول.' }
        }
      }

      // إذا نجح تسجيل الدخول، احصل على بيانات المستخدم من قاعدة البيانات
      if (data.user) {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select(`
            *,
            role:roles(*),
            department:departments(*)
          `)
          .eq('id', data.user.id)
          .single()

        if (userError) {
          console.warn('Could not fetch user data:', userError)
        }

        if (userData) {
          // دمج بيانات المستخدم مع session
          const enhancedSession = {
            ...data.session,
            user: {
              ...data.user,
              user_metadata: {
                ...data.user.user_metadata,
                name: userData.name,
                role: userData.role?.name,
                department: userData.department?.name,
                department_id: userData.department_id,
                role_id: userData.role_id
              }
            }
          }

          return {
            data: { user: enhancedSession.user, session: enhancedSession },
            error: null
          }
        }
      }

      return { data, error: null }
    } catch (error) {
      console.error('Sign in error:', error)
      return {
        data: { user: null, session: null },
        error: { message: 'حدث خطأ أثناء تسجيل الدخول' }
      }
    }
  },

  /**
   * تسجيل مستخدم جديد
   */
  signUp: async (email: string, password: string, userData?: any) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      })

      return { data, error }
    } catch (error) {
      return {
        data: { user: null, session: null },
        error: { message: 'فشل في إنشاء الحساب' }
      }
    }
  },

  /**
   * تسجيل الخروج
   */
  signOut: async () => {
    try {
      const { error } = await supabase.auth.signOut()
      return { error }
    } catch (error) {
      return { error: { message: 'فشل في تسجيل الخروج' } }
    }
  },

  /**
   * الحصول على المستخدم الحالي
   */
  getCurrentUser: async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      return { user, error }
    } catch (error) {
      return { user: null, error: { message: 'فشل في الحصول على بيانات المستخدم' } }
    }
  },

  /**
   * الحصول على الجلسة الحالية
   */
  getCurrentSession: async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      return { session, error }
    } catch (error) {
      return { session: null, error: { message: 'فشل في الحصول على الجلسة' } }
    }
  },

  /**
   * مراقبة تغييرات المصادقة
   */
  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback)
  }
}

// دوال مساعدة لقاعدة البيانات
export const database = {
  /**
   * الحصول على جميع المستخدمين
   */
  getUsers: async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          *,
          role:roles(*),
          department:departments(*)
        `)

      return { data, error }
    } catch (error) {
      return { data: null, error: { message: 'فشل في جلب المستخدمين' } }
    }
  },

  /**
   * الحصول على مستخدم بواسطة المعرف
   */
  getUserById: async (id: string) => {
    try {
      console.log('getUserById: Fetching user with id:', id)

      // جلب بيانات المستخدم مع العلاقات
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select(`
          *,
          role:roles(*),
          department:departments(*)
        `)
        .eq('id', id)
        .single()

      console.log('getUserById: User query result:', { userData, userError })

      if (userError) {
        return { data: null, error: userError }
      }

      return {
        data: userData,
        error: null
      }
    } catch (error) {
      console.error('getUserById error:', error)
      return { data: null, error: { message: 'فشل في جلب بيانات المستخدم' } }
    }
  },

  /**
   * إنشاء طلب مشروع جديد
   */
  createProjectRequest: async (requestData: any) => {
    try {
      const { data, error } = await supabase
        .from('project_requests')
        .insert(requestData)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      return { data: null, error: { message: 'فشل في إنشاء طلب المشروع' } }
    }
  },

  /**
   * الحصول على طلبات المشاريع
   */
  getProjectRequests: async (filters?: any) => {
    try {
      let query = supabase
        .from('project_requests')
        .select(`
          *,
          requester:users!requester_id(*),
          department:departments(*)
        `)

      if (filters?.status) {
        query = query.eq('status', filters.status)
      }

      if (filters?.main_type) {
        query = query.eq('main_type', filters.main_type)
      }

      const { data, error } = await query.order('created_at', { ascending: false })

      return { data, error }
    } catch (error) {
      return { data: null, error: { message: 'فشل في جلب طلبات المشاريع' } }
    }
  }
}

// تصدير معلومات حالة النظام
export const systemInfo = {
  supabaseUrl,
  isConnected: true
}

// دالة فحص مصدر البيانات
export const checkDataSource = () => {
  console.log('=== Data Source Check ===')
  console.log('Supabase URL:', supabaseUrl || 'Not configured')
  console.log('Environment Variables:', {
    SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    DEVELOPMENT_MODE: process.env.DEVELOPMENT_MODE
  })

  console.log('🚀 Using REAL SUPABASE DATABASE')
  console.log('Database URL:', supabaseUrl)

  return {
    supabaseUrl,
    hasEnvVars: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    dataSource: 'SUPABASE_REAL'
  }
}